package com.gettrade.start;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

import com.sun.jna.Library;
import com.sun.jna.Native;

public class PocketOption {

	public interface Trade extends Library {
		Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

		public void TradeBitqDlg();
		public void TradeStart(int nflag);
	}
	public static int BUY_UP = 400001;
	public static int SELL_DOWN = 400002;
	public static int TIME_RESET = 4;
	public static boolean bFirst = true;
		
    public static void main(String[] args) {
		Trade.INSTANCE.TradeBitqDlg();
		Trade.INSTANCE.TradeStart(TIME_RESET);
		
		try {
			Thread.sleep(15000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		//Trade.INSTANCE.TradeStart(1);
        ProcessBuilder pb = new ProcessBuilder("python","PocketOption.py");
        Process p = null;
        try {
    		int TradeNum = 0;
    		boolean bTrade_rule = true;
            double oldPrice = 0; 
            double trade_price = 0;
            double price = 0.0, openPrice = 0.0;
            long currentMinute = -1;
            long startMinute = System.currentTimeMillis() / 1000 / 60;
            p = pb.start();
            BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String ret;           
            while ((ret = in.readLine()) != null) {

                if (ret.contains("BTCUSD_otc")) {
                    ret = ret.replace("[", "").replace("]", "").replace("'", "");
                    String[] parts = ret.split(",");
                    //double time = Double.parseDouble(parts[1].trim());
                    price = Double.parseDouble(parts[2].trim());
                    long newMinute = (System.currentTimeMillis() / 1000 / 60);

                    if (newMinute - startMinute < 1) {
                        continue;
                    }
                    
                    if (newMinute != currentMinute) {
                        currentMinute = newMinute;
                        openPrice = price;
                        
                    }else if (oldPrice != 0) {
                    	if(TradeNum == 1 && oldPrice > price && Math.abs(oldPrice - price) >= 0.007) {
                    		Trade.INSTANCE.TradeStart(SELL_DOWN);
                    		System.out.println("SELL openPrice: " + openPrice +" Current price: " + price +" ABS: " + String.format("%.3f", Math.abs(oldPrice - price)));
                    	}else if (TradeNum == 2 && oldPrice < price && Math.abs(oldPrice - price) >= 0.007) {
                    		Trade.INSTANCE.TradeStart(BUY_UP);
                    		System.out.println("BUY openPrice: " + openPrice + " Current price: " + price + " ABS: "+ String.format("%.3f", Math.abs(oldPrice - price)));
						}else {
							if(oldPrice < price) {
								TradeNum=1;
								System.out.println("UP openPrice: " + openPrice +" Current price: " + price +" ABS: " + String.format("%.3f", Math.abs(oldPrice - price)));
							}else if(oldPrice > price) {
								TradeNum=2;
								System.out.println("DN openPrice: " + openPrice +" Current price: " + price +" ABS: " + String.format("%.3f", Math.abs(oldPrice - price)));
							}else
								TradeNum=0;
								System.out.println("NO openPrice: " + openPrice +" Current price: " + price +" ABS: " + String.format("%.3f", Math.abs(oldPrice - price)));
						}
                    }else {
                    	System.out.println("Checking: trade_price: " + trade_price +" price: " + price +" TradeNum: " + TradeNum +" bTrade_rule: " + bTrade_rule);
                    }
                
                    oldPrice = price;
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
        

		/*
        ProcessBuilder pb = new ProcessBuilder("python","PocketOption.py");
        Process p = null;
        try {
            p = pb.start();
            
            BufferedReader in = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String ret;
            double price = 0.0, openPrice = 0.0, highPrice = 0.0, lowPrice = 0.0, oldclosePrice = 0.0;
            long currentMinute = -1;
            long startMinute = System.currentTimeMillis() / 1000 / 60;
            
            while ((ret = in.readLine()) != null) {
                long newMinute = (System.currentTimeMillis() / 1000 / 60);

                if (newMinute - startMinute < 1) {
                    continue;
                }
				
                if (ret.contains("BTCUSD_otc")) {
                    ret = ret.replace("[", "").replace("]", "").replace("'", "");
                    String[] parts = ret.split(",");
                    price = Double.parseDouble(parts[2].trim());

                    if (newMinute != currentMinute) {
                        currentMinute = newMinute;
                        openPrice = price;
                        highPrice = price;
                        lowPrice = price;
                        oldclosePrice = price;
                    } else  {
                    	
        				long currentSecond = (System.currentTimeMillis()  / 1000) % 60;
                        highPrice = Math.max(highPrice, price);
                        lowPrice = Math.min(lowPrice, price);
        				double h = highPrice - openPrice;
        				double s = openPrice - lowPrice;

        				if (h > s && price > highPrice) {
        					Trade.INSTANCE.TradeStart(BUY_UP);
        					System.out.println("TRADE BUY: " + currentSecond +" Current price: " + price +" Open price: " + openPrice + " High price" + highPrice+ " Low price" + lowPrice);
        				} else if(h < s && price < lowPrice) {
        					Trade.INSTANCE.TradeStart(SELL_DOWN);
        						System.out.println("TRADE SELL: " + currentSecond +" Current price: " + price +" Open price: " + openPrice + " High price" + highPrice+ " Low price" + lowPrice);

        					}

        				oldclosePrice = price;
                    }
                }
            }
            
        } catch (IOException e) {
            e.printStackTrace();
        }
	*/
		
    }
}
