package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Deque;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class expertoption {

    public enum TradeSignal {
        UP, DOWN, HOLD
    }

    public enum TradeStatus {
        WAITING, // 거래 대기
        TRADING, // 거래 진행 중
        STOPPED_BY_VOLUME, // 거래량으로 인한 거래 중지
        STOPPED_BY_CYCLE,  // UP/DOWN 1회 완료 후 중지
        STOPPED_MANUALLY // 수동 중지
    }

    // JNA 인터페이스: 외부 DLL 라이브러리 연동
    public interface Trade extends Library {
        Trade INSTANCE = Native.loadLibrary("MTrade", Trade.class);

        void TradeBitqDlg_expert();
        int GetUpDownSecRcv();
        void TradeStart(int nflag);
    }

    // 거래 상수
    public static final int BUY_UP = 500002;
    public static final int SELL_DOWN = 500001;
    public static final int CHECK = 500003;
    public static final int CHECK_PAYOUT = 500004;
    public static final int CHECK_RESULT = 500005; // 거래 결과 확인 (가상)
    public static final int WINDOWS_TOP = 300008;
    public static final int TIME_RESET = 4;
    public static final int REFRESH = 304;
    public static final int TRADE_STOP = 103;

    // 설정 및 상태 변수
    public static volatile double volume = 0;
    public static volatile double volumeBTC = 0;
    public static volatile double volumeBTC_Check = 20;
    public static volatile double trade_volume_max = 200;
    public static volatile TradeStatus tradeStatus = TradeStatus.WAITING;
    public static volatile int isTradingEnabled = 0;
    public static volatile int TRACE = 0;


    // 지표 공유 변수
    public static volatile double sharedEmaSpot = 0.0;
    public static volatile double sharedRsi5 = 0.0;
    public static volatile double priceROC = 0.0;
    public static volatile double sharedEmaFutures = 0.0;
    public static volatile double standardDeviation = 0.0; // 표준편차
    public static volatile double atrValue = 0.0; // ATR 값

    // 바이낸스 클라이언트
    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;
    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;

    private static AtomicBoolean initTrade = new AtomicBoolean(false);
    private static AtomicBoolean first = new AtomicBoolean(false);
    private static AtomicBoolean hasTradedUpInCycle = new AtomicBoolean(false);
    private static AtomicBoolean hasTradedDownInCycle = new AtomicBoolean(false);
    public static String symbol = "BTCUSDT";

    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- 지표 계산 클래스들 (RSI, EMA, ROC) ---
    public static class RSI {
        private final int period;
        private Double avgGain = null;
        private Double avgLoss = null;

        public RSI(int period) {
            this.period = period;
        }

        public double calculate(List<Double> prices) {
            if (prices.size() < period + 1) {
                return 50.0;
            }

            if (avgGain == null) { // 첫 계산
                double firstGains = 0;
                double firstLosses = 0;
                for (int i = 1; i <= period; i++) {
                    double change = prices.get(i) - prices.get(i - 1);
                    if (change > 0) {
                        firstGains += change;
                    } else {
                        firstLosses += Math.abs(change);
                    }
                }
                avgGain = firstGains / period;
                avgLoss = firstLosses / period;
            } else { // Wilder's Smoothing
                double lastPrice = prices.get(prices.size() - 1);
                double prevPrice = prices.get(prices.size() - 2);
                double change = lastPrice - prevPrice;
                double gain = change > 0 ? change : 0;
                double loss = change < 0 ? Math.abs(change) : 0;

                avgGain = (avgGain * (period - 1) + gain) / period;
                avgLoss = (avgLoss * (period - 1) + loss) / period;
            }

            if (avgLoss == 0) {
                return 100.0;
            }
            double rs = avgGain / avgLoss;
            return 100 - (100 / (1 + rs));
        }
    }

    public static class EMA {
        private final double multiplier;
        private Double previousEMA = null;
        public EMA(int period) { this.multiplier = 2.0 / (period + 1); }
        public void calculate(double price) {
            if (previousEMA == null) {
                previousEMA = price;
            } else {
                previousEMA = (price - previousEMA) * multiplier + previousEMA;
            }
        }
        public double get() { return previousEMA != null ? previousEMA : 0.0; }
    }
    
    public static double calculateROC(double currentPrice, double previousPrice) {
        if (previousPrice == 0) return 0;
        return ((currentPrice - previousPrice) / previousPrice) * 100;
    }

    public static class ATR {
        private final int period;
        public ATR(int period) { this.period = period; }
        public double calculate(List<Candlestick> candles) {
            if (candles.size() < period) return 0.0;
            double sumTR = 0;
            for (int i = candles.size() - period; i < candles.size(); i++) {
                double high = Double.parseDouble(candles.get(i).getHigh());
                double low = Double.parseDouble(candles.get(i).getLow());
                double prevClose = Double.parseDouble(candles.get(i - 1).getClose());
                double tr = Math.max(high - low, Math.max(Math.abs(high - prevClose), Math.abs(low - prevClose)));
                sumTR += tr;
            }
            return sumTR / period;
        }
    }

    public static double calculateStandardDeviation(List<Double> prices, int period) {
        if (prices.size() < period) return 0.0;
        double sum = 0.0;
        for (int i = prices.size() - period; i < prices.size(); i++) {
            sum += prices.get(i);
        }
        double mean = sum / period;
        double standardDeviation = 0.0;
        for (int i = prices.size() - period; i < prices.size(); i++) {
            standardDeviation += Math.pow(prices.get(i) - mean, 2);
        }
        return Math.sqrt(standardDeviation / period);
    }


    // --- 주 거래 로직 ---
    static class MessageHandler implements Runnable {
        private volatile double tradeOrderbookDownF20 = Double.NaN;
        private volatile double tradeOrderbookUpF20 = Double.NaN;
        private volatile double tradeOrderbookDown20 = Double.NaN;
        private volatile double tradeOrderbookUp20 = Double.NaN;
        private volatile double currentFuturesPrice = -999999;
        private volatile double currentSpotPrice = -999999;
        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;
        private int lastInitSecond = -1; // 5초 주기 실행을 제어하기 위한 플래그

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Socket connection successful. Starting data reception.");
                    resetDataFlags();

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty()) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            // 매 분 59초 또는 첫 실행 시 전체 초기화
                            if ((currentSecond == 59 || !first.get())) {
                                if (initTrade.compareAndSet(false, true)) {
                                    messageProcessingService.submit(() -> {
                                        try {
                                            InitMain(); // 설정 및 데이터 업데이트
                                            resetTradeOrderbook();
                                            resetDataFlags();
                                            
                                            // 사이클 상태 관리
                                            if (tradeStatus != TradeStatus.STOPPED_BY_CYCLE && tradeStatus != TradeStatus.STOPPED_MANUALLY) {
                                                tradeStatus = TradeStatus.WAITING; 
                                            }
                                            
                                            asyncPrint("Full initialization completed. Trade status: " + tradeStatus);
                                            first.set(true);
                                            lastInitSecond = (int) currentSecond; // 마지막 실행 시간 기록
                                        } catch (Exception e) {
                                            asyncPrint("Error during full initialization: " + e.getMessage());
                                        } finally {
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }
                            // 매 5초마다 데이터 업데이트 (59초 제외)
                            else if (currentSecond % 5 == 0 && currentSecond != 59 && lastInitSecond != currentSecond) {
                                if (initTrade.compareAndSet(false, true)) {
                                    lastInitSecond = (int) currentSecond; // 실행 시간 기록
                                    messageProcessingService.submit(() -> {
                                        try {
                                            InitMain(); 
                                            if (TRACE > 1) asyncPrint("Periodic 5-second data update completed.");
                                        } catch (Exception e) {
                                            asyncPrint("Error during periodic 5s reset: " + e.getMessage());
                                        } finally {
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            }
                            // 그 외 시간에는 메시지 처리
                            else if (first.get()) {
                                processMessage(message);
                            }
                        }
                    }
                } catch (IOException e) {
                    handleConnectionError(e);
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    safeSleep(1000);
                }
            }
        }

        private void processMessage(String message) {
            if (message == null || message.isEmpty()) return;
            // JSON 파싱
            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);
                while (reader.hasNext()) {
                    JsonObject jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();
                        parseOrderbookData(mtype, msg);
                        // 모든 데이터가 수신되었고 거래 가능한 상태일 때만 로직 실행
                        if (bfirstBTC20 && bfirstFBTC20 && tradeStatus == TradeStatus.WAITING) {
                            if (volume > trade_volume_max || volumeBTC > volumeBTC_Check) {
                                if (TRACE > 0) asyncPrint("Trading stopped due to volume limit exceeded. (Futures:" + volume + ", Spot:" + volumeBTC + ")");
                                tradeStatus = TradeStatus.STOPPED_BY_VOLUME;
                                return;
                            }

                            TradeSignal signal = determineTradeAction();
                            if (signal != TradeSignal.HOLD) {
                                if(executeTrade(signal)) break; // 거래가 성공적으로 실행되면 루프 종료
                            }
                        }
                    }
                }
            } catch (Exception e) {
                 if (TRACE > 0) asyncPrint("JSON parsing error: " + e.getMessage());
            }
        }
        
        private void parseOrderbookData(String mtype, String msg) {
            try {
                String[] data = msg.split("-", 3);
                if (data.length >= 3) {
                    if ("BTC20".equals(mtype)) {
                        tradeOrderbookDown20 = Double.parseDouble(data[0]);
                        tradeOrderbookUp20 = Double.parseDouble(data[1]);
                        currentSpotPrice = Double.parseDouble(data[2]);
                        bfirstBTC20 = true;
                    } else if ("FBTC20".equals(mtype)) {
                        tradeOrderbookDownF20 = Double.parseDouble(data[0]);
                        tradeOrderbookUpF20 = Double.parseDouble(data[1]);
                        currentFuturesPrice = Double.parseDouble(data[2]);
                        bfirstFBTC20 = true;
                    }
                }
            } catch (NumberFormatException e) {
                if (TRACE > 0) asyncPrint("Number format error: " + msg);
            }
        }

        private TradeSignal determineTradeAction() {
            // 1. 오더북 불균형
            double imbalance = calculateAdvancedOrderbookImbalance();
            boolean strongBuyPressure = imbalance > 0.15;
            boolean strongSellPressure = imbalance < -0.15;

            // 2. 동적 임계값 (ATR 기반)
            double dynamicThreshold = Math.max(0.005, Math.min(0.015, atrValue * 0.1));

            // 3. 볼린저 밴드
            double upperBand = sharedEmaSpot + (2 * standardDeviation);
            double lowerBand = sharedEmaSpot - (2 * standardDeviation);

            // 4. 가격 모멘텀 (ROC + 볼린저 밴드 필터)
            boolean shortTermBuySignal = priceROC > dynamicThreshold && currentSpotPrice < upperBand;
            boolean shortTermSellSignal = priceROC < -dynamicThreshold && currentSpotPrice > lowerBand;
            
            // 5. RSI 필터 (과매수/과매도 구간 회피)
            boolean rsiFilter = sharedRsi5 > 25 && sharedRsi5 < 75;

            // 6. 중기 추세 (EMA 기준)
            boolean mediumTermUpTrend = currentSpotPrice > sharedEmaSpot && currentFuturesPrice > sharedEmaFutures;
            boolean mediumTermDownTrend = currentSpotPrice < sharedEmaSpot && currentFuturesPrice < sharedEmaFutures;

            // 최종 신호 결정
            if (shortTermBuySignal && mediumTermUpTrend && strongBuyPressure && rsiFilter) {
                if (TRACE > 0) asyncPrint(String.format("### UP Signal ### ROC:%.4f, Thresh:%.4f, IMB:%.2f, RSI:%.2f", priceROC, dynamicThreshold, imbalance, sharedRsi5));
                return TradeSignal.UP;
            }

            if (shortTermSellSignal && mediumTermDownTrend && strongSellPressure && rsiFilter) {
                 if (TRACE > 0) asyncPrint(String.format("### DOWN Signal ### ROC:%.4f, Thresh:%.4f, IMB:%.2f, RSI:%.2f", priceROC, dynamicThreshold, imbalance, sharedRsi5));
                return TradeSignal.DOWN;
            }

            if (TRACE > 1) asyncPrint(String.format("Waiting for signal | ROC:%.4f, Thresh:%.4f, IMB:%.2f, RSI:%.2f", priceROC, dynamicThreshold, imbalance, sharedRsi5));
            return TradeSignal.HOLD;
        }

        private double calculateAdvancedOrderbookImbalance() {
            if (Double.isNaN(tradeOrderbookDown20) || Double.isNaN(tradeOrderbookUp20) ||
                Double.isNaN(tradeOrderbookDownF20) || Double.isNaN(tradeOrderbookUpF20)) {
                return 0;
            }
            double totalBid = (tradeOrderbookDown20 * 0.5) + (tradeOrderbookDownF20 * 0.5);
            double totalAsk = (tradeOrderbookUp20 * 0.5) + (tradeOrderbookUpF20 * 0.5);
            if (totalBid + totalAsk == 0) return 0;
            return (totalBid - totalAsk) / (totalBid + totalAsk);
        }

        private boolean executeTrade(TradeSignal signal) {
            if (isTradingEnabled <= 0) {
                if (TRACE > 0) asyncPrint("Trading is disabled.");
                return false;
            }
        
            // 이미 거래가 진행중이거나, 사이클/거래량으로 중지된 상태면 실행하지 않음
            if (tradeStatus != TradeStatus.WAITING) {
                if (TRACE > 1) asyncPrint("Current trade status (" + tradeStatus + ") is not WAITING, so new trade will not be executed.");
                return false;
            }
        
            // 사이클 내에서 이미 거래한 방향으로는 재진입 방지
            if ((signal == TradeSignal.UP && hasTradedUpInCycle.get()) || (signal == TradeSignal.DOWN && hasTradedDownInCycle.get())) {
                if (TRACE > 0) asyncPrint("Already executed " + signal + " trade in this cycle. Preventing duplicate trades.");
                return false;
            }
        
            tradeStatus = TradeStatus.TRADING; // 상태를 '거래 중'으로 변경
        
            if (signal == TradeSignal.UP) {
                hasTradedUpInCycle.set(true);
                Trade.INSTANCE.TradeStart(BUY_UP);
                asyncPrint(String.format("### UP Order Executed ### | Price(Spot/Futures): %.2f/%.2f", currentSpotPrice, currentFuturesPrice));
            } else if (signal == TradeSignal.DOWN) {
                hasTradedDownInCycle.set(true);
                Trade.INSTANCE.TradeStart(SELL_DOWN);
                asyncPrint(String.format("### DOWN Order Executed ### | Price(Spot/Futures): %.2f/%.2f", currentSpotPrice, currentFuturesPrice));
            }
        
            // 30초 후 거래 상태를 복구하고 사이클 완료를 체크하는 스레드
            new Thread(() -> {
                safeSleep(30000); // 30초 대기
        
                // UP/DOWN 거래가 모두 한 번씩 완료되었는지 확인
                if (hasTradedUpInCycle.get() && hasTradedDownInCycle.get()) {
                    tradeStatus = TradeStatus.STOPPED_BY_CYCLE;
                    asyncPrint("UP/DOWN trading cycle completed. Trading stopped until next initialization.");
                } else {
                    tradeStatus = TradeStatus.WAITING; // 사이클 미완료 시, 다음 거래를 위해 '대기' 상태로 전환
                    if (TRACE > 0) asyncPrint("Trade completed. Switching to waiting state for next signal.");
                }
            }).start();

            return ( hasTradedDownInCycle.get() || hasTradedUpInCycle.get() );
        }

        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = Double.NaN;
            tradeOrderbookUpF20 = Double.NaN;
            tradeOrderbookDown20 = Double.NaN;
            tradeOrderbookUp20 = Double.NaN;
            currentFuturesPrice = -999999;
            currentSpotPrice = -999999;
        }

        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        private void handleConnectionError(IOException e) {
            asyncPrint("Socket connection error: " + e.getMessage());
            resetTradeOrderbook();
            resetDataFlags();
            initTrade.set(false);
            first.set(false);
            safeSleep(5000); // 5초 후 재연결 시도
        }
        
        private void safeSleep(long millis) {
            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    public static void InitMain() {
        // 사이클이 완료되었으면 플래그 초기화
        if (tradeStatus == TradeStatus.STOPPED_BY_CYCLE) {
            hasTradedUpInCycle.set(false);
            hasTradedDownInCycle.set(false);
            tradeStatus = TradeStatus.WAITING; // 다음 거래를 위해 WAITING 상태로 전환
            asyncPrint("Starting new trading cycle. Resetting UP/DOWN flags and changing trade status to WAITING.");
        }

        try {
            // 1. 데이터 가져오기 (현물/선물 모두 1분봉)
            List<Candlestick> candlestickListFutures = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);
            List<Candlestick> candlestickListSpot = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 100, null, null);

            // 2. 거래량 계산 (10개 캔들 평균)
            volume = calculateAverageVolume(candlestickListFutures, 10);
            volumeBTC = calculateAverageVolume(candlestickListSpot, 10);

            // 가격 데이터 리스트 생성
            List<Double> priceListSpot = new ArrayList<>();
            candlestickListSpot.forEach(c -> priceListSpot.add(Double.parseDouble(c.getClose())));
            List<Double> priceListFutures = new ArrayList<>();
            candlestickListFutures.forEach(c -> priceListFutures.add(Double.parseDouble(c.getClose())));

            // 3. 지표 계산 (1분봉 데이터 기준)
            RSI rsi5Calculator = new RSI(5);
            if (priceListSpot.size() >= 5) {
                EMA ema5 = new EMA(5);
                priceListSpot.forEach(ema5::calculate);
                sharedEmaSpot = ema5.get();
            }
            if (priceListSpot.size() >= 6) { // RSI는 변화량을 계산하므로 n+1개의 데이터 필요
                sharedRsi5 = rsi5Calculator.calculate(priceListSpot);
            }
            if (priceListSpot.size() >= 2) {
                priceROC = calculateROC(priceListSpot.get(priceListSpot.size() - 1), priceListSpot.get(priceListSpot.size() - 2));
            }
            if (candlestickListSpot.size() >= 15) { // ATR, SD 계산을 위한 충분한 데이터 확인 (14+1)
                 ATR atr14 = new ATR(14);
                 atrValue = atr14.calculate(candlestickListSpot);
                 standardDeviation = calculateStandardDeviation(priceListSpot, 14);
            }
            if (priceListFutures.size() >= 10) {
                EMA ema10F = new EMA(10);
                priceListFutures.forEach(ema10F::calculate);
                sharedEmaFutures = ema10F.get();
            }

            // 4. 설정 파일 로드
            loadProperties();

            // 5. 로그 출력
            if (TRACE > 0) {
                asyncPrint("--- System Status Update (1-Minute Bars) ---");
                asyncPrint(String.format("Volume(Spot/Futures): %.2f / %.2f (Limit: %.2f / %.2f)", volumeBTC, volume, volumeBTC_Check, trade_volume_max));
                asyncPrint(String.format("Indicators(Spot) -> EMA: %.2f, RSI: %.2f, ROC: %.4f, SD: %.4f, ATR: %.4f", sharedEmaSpot, sharedRsi5, priceROC, standardDeviation, atrValue));
                asyncPrint(String.format("Indicators(Futures) -> EMA: %.2f", sharedEmaFutures));
                asyncPrint("Trading Enabled: " + isTradingEnabled + " | Trace Level: " + TRACE);
                asyncPrint("--------------------------");
            }
        } catch (Exception e) {
            asyncPrint("Error occurred during InitMain execution: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static double calculateAverageVolume(List<Candlestick> candlesticks, int count) {
        if (candlesticks == null || candlesticks.size() < count + 1) return 0;
        double totalVolume = 0;
        for (int i = candlesticks.size() - (count + 1); i < candlesticks.size() - 1; i++) {
            totalVolume += Double.parseDouble(candlesticks.get(i).getVolume());
        }
        return roundToTwoDecimalPlaces(totalVolume / count);
    }
    
    private static void loadProperties() {
        String filePath = "c:/Trade/TradeExpert.properties";
        try (InputStream input = new FileInputStream(filePath)) {
            Properties prop = new Properties();
            prop.load(input);
            volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "20"));
            trade_volume_max = Double.parseDouble(prop.getProperty("trade_volume_max", "200.0"));
            isTradingEnabled = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
            TRACE = Integer.parseInt(prop.getProperty("TRACE", "0"));
        } catch (IOException | NumberFormatException e) {
            asyncPrint("Configuration file loading error: " + e.getMessage());
        }
    }

    public static void main(String[] args) throws InterruptedException {
        // 바이낸스 클라이언트 생성
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();
        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        // UI 실행
        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP);
        Trade.INSTANCE.TradeStart(TIME_RESET);

        asyncPrint("Waiting for initialization (10 seconds)...");
        Thread.sleep(10000);
        asyncPrint("Initialization completed.");

        // 메시지 핸들러 스레드 시작
        MessageHandler messageHandlerInstance = new MessageHandler();
        Thread messageHandlerThread = new Thread(messageHandlerInstance, "MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");
    }
    
    private static double roundToTwoDecimalPlaces(double value) {
        return Math.round(value * 100.0) / 100.0;
    }
}
