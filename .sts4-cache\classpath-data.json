{"name": "gettrade", "classpathEntries": [{"kind": "binary", "path": "C:\\Program Files\\Java\\jdk-14.0.2\\lib\\jrt-fs.jar", "sourceContainerUrl": "file:/C:/Program%20Files/Java/jdk-14.0.2/lib/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/14/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-core\\5.0.2\\xchange-core-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-core/5.0.2/xchange-core-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-core/5.0.2/xchange-core-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\mmazi\\rescu\\2.0.2\\rescu-2.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/github/mmazi/rescu/2.0.2/rescu-2.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/github/mmazi/rescu/2.0.2/rescu-2.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\oauth\\signpost\\signpost-core\\1.2.1.2\\signpost-core-1.2.1.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/oauth/signpost/signpost-core/1.2.1.2/signpost-core-1.2.1.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/oauth/signpost/signpost-core/1.2.1.2/signpost-core-1.2.1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-io\\commons-io\\2.7\\commons-io-2.7.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-io/commons-io/2.7/commons-io-2.7-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-io/commons-io/2.7/commons-io-2.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-all\\1.5.0\\resilience4j-all-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-all/1.5.0/resilience4j-all-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-all/1.5.0/resilience4j-all-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\vavr\\vavr\\0.10.2\\vavr-0.10.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/vavr/vavr/0.10.2/vavr-0.10.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/vavr/vavr/0.10.2/vavr-0.10.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\vavr\\vavr-match\\0.10.2\\vavr-match-0.10.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/vavr/vavr-match/0.10.2/vavr-match-0.10.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/vavr/vavr-match/0.10.2/vavr-match-0.10.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-ratelimiter\\1.5.0\\resilience4j-ratelimiter-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ratelimiter/1.5.0/resilience4j-ratelimiter-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-ratelimiter/1.5.0/resilience4j-ratelimiter-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-core\\1.5.0\\resilience4j-core-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-core/1.5.0/resilience4j-core-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-core/1.5.0/resilience4j-core-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-circuitbreaker\\1.5.0\\resilience4j-circuitbreaker-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circuitbreaker/1.5.0/resilience4j-circuitbreaker-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-circuitbreaker/1.5.0/resilience4j-circuitbreaker-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-bulkhead\\1.5.0\\resilience4j-bulkhead-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-bulkhead/1.5.0/resilience4j-bulkhead-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-bulkhead/1.5.0/resilience4j-bulkhead-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-retry\\1.5.0\\resilience4j-retry-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-retry/1.5.0/resilience4j-retry-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-retry/1.5.0/resilience4j-retry-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-cache\\1.5.0\\resilience4j-cache-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-cache/1.5.0/resilience4j-cache-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-cache/1.5.0/resilience4j-cache-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\cache\\cache-api\\1.1.0\\cache-api-1.1.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.0/cache-api-1.1.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/cache/cache-api/1.1.0/cache-api-1.1.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\github\\resilience4j\\resilience4j-timelimiter\\1.5.0\\resilience4j-timelimiter-1.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-timelimiter/1.5.0/resilience4j-timelimiter-1.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/github/resilience4j/resilience4j-timelimiter/1.5.0/resilience4j-timelimiter-1.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-api\\1.7.30\\slf4j-api-1.7.30.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\annotation\\javax.annotation-api\\1.3.2\\javax.annotation-api-1.3.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\ws\\rs\\javax.ws.rs-api\\2.1.1\\javax.ws.rs-api-2.1.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-bitmex\\5.0.2\\xchange-bitmex-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-bitmex/5.0.2/xchange-bitmex-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-bitmex/5.0.2/xchange-bitmex-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-stream-core\\5.0.2\\xchange-stream-core-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-core/5.0.2/xchange-stream-core-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-core/5.0.2/xchange-stream-core-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-stream-service-core\\5.0.2\\xchange-stream-service-core-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-service-core/5.0.2/xchange-stream-service-core-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-service-core/5.0.2/xchange-stream-service-core-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\reactivex\\rxjava2\\rxjava\\2.2.19\\rxjava-2.2.19.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.19/rxjava-2.2.19-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/reactivex/rxjava2/rxjava/2.2.19/rxjava-2.2.19-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\reactivestreams\\reactive-streams\\1.0.3\\reactive-streams-1.0.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-stream-service-netty\\5.0.2\\xchange-stream-service-netty-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-service-netty/5.0.2/xchange-stream-service-netty-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-service-netty/5.0.2/xchange-stream-service-netty-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\knowm\\xchange\\xchange-stream-bitmex\\5.0.2\\xchange-stream-bitmex-5.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-bitmex/5.0.2/xchange-stream-bitmex-5.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/knowm/xchange/xchange-stream-bitmex/5.0.2/xchange-stream-bitmex-5.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\fluent-hc\\4.5.12\\fluent-hc-4.5.12.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.12/fluent-hc-4.5.12-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/fluent-hc/4.5.12/fluent-hc-4.5.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\github\\j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\\bitfinex-v2-wss-api\\0.7.6-snapshot\\bitfinex-v2-wss-api-0.7.6-snapshot.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\sumzerotrading\\bitmex-client\\1.0.0-SNAPSHOT\\bitmex-client-1.0.0-SNAPSHOT.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\sumzerotrading\\sumzero-market-data-api\\0.1.7-SNAPSHOT\\sumzero-market-data-api-0.1.7-SNAPSHOT.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\sumzerotrading\\sumzero-commons-api\\0.1.7-SNAPSHOT\\sumzero-commons-api-0.1.7-SNAPSHOT.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\tyrus\\bundles\\tyrus-standalone-client\\1.13.1\\tyrus-standalone-client-1.13.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/tyrus/bundles/tyrus-standalone-client/1.13.1/tyrus-standalone-client-1.13.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/tyrus/bundles/tyrus-standalone-client/1.13.1/tyrus-standalone-client-1.13.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\websocket\\websocket-client\\9.4.7.RC0\\websocket-client-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.7.RC0/websocket-client-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.7.RC0/websocket-client-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-client\\9.4.7.RC0\\jetty-client-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.7.RC0/jetty-client-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.7.RC0/jetty-client-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-http\\9.4.7.RC0\\jetty-http-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.7.RC0/jetty-http-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.7.RC0/jetty-http-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-xml\\9.4.7.RC0\\jetty-xml-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-xml/9.4.7.RC0/jetty-xml-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-xml/9.4.7.RC0/jetty-xml-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-util\\9.4.7.RC0\\jetty-util-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.7.RC0/jetty-util-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.7.RC0/jetty-util-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\jetty-io\\9.4.7.RC0\\jetty-io-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.7.RC0/jetty-io-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.7.RC0/jetty-io-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\websocket\\websocket-common\\9.4.7.RC0\\websocket-common-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.7.RC0/websocket-common-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.7.RC0/websocket-common-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\eclipse\\jetty\\websocket\\websocket-api\\9.4.7.RC0\\websocket-api-9.4.7.RC0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.7.RC0/websocket-api-9.4.7.RC0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.7.RC0/websocket-api-9.4.7.RC0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\hamcrest\\hamcrest-core\\1.3\\hamcrest-core-1.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\gson\\gson\\2.8.1\\gson-2.8.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.1/gson-2.8.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.1/gson-2.8.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-annotations\\2.9.3\\jackson-annotations-2.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.3/jackson-annotations-2.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.3/jackson-annotations-2.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\log4j\\log4j\\1.2.17\\log4j-1.2.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\guava\\27.1-jre\\guava-27.1-jre.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/guava/guava/27.1-jre/guava-27.1-jre-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/guava/guava/27.1-jre/guava-27.1-jre-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\failureaccess\\1.0.1\\failureaccess-1.0.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\guava\\listenablefuture\\9999.0-empty-to-avoid-conflict-with-guava\\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\code\\findbugs\\jsr305\\3.0.2\\jsr305-3.0.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\checkerframework\\checker-qual\\2.5.2\\checker-qual-2.5.2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/2.5.2/checker-qual-2.5.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\errorprone\\error_prone_annotations\\2.2.0\\error_prone_annotations-2.2.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.2.0/error_prone_annotations-2.2.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\google\\j2objc\\j2objc-annotations\\1.1\\j2objc-annotations-1.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.1/j2objc-annotations-1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\codehaus\\mojo\\animal-sniffer-annotations\\1.17\\animal-sniffer-annotations-1.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.17/animal-sniffer-annotations-1.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\jersey\\core\\jersey-client\\2.27\\jersey-client-2.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.27/jersey-client-2.27-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.27/jersey-client-2.27-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\jersey\\core\\jersey-common\\2.27\\jersey-common-2.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.27/jersey-common-2.27-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.27/jersey-common-2.27-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\osgi-resource-locator\\1.0.1\\osgi-resource-locator-1.0.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\external\\javax.inject\\2.5.0-b42\\javax.inject-2.5.0-b42.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.5.0-b42/javax.inject-2.5.0-b42-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.5.0-b42/javax.inject-2.5.0-b42-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\jersey\\inject\\jersey-hk2\\2.27\\jersey-hk2-2.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/inject/jersey-hk2/2.27/jersey-hk2-2.27-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/inject/jersey-hk2/2.27/jersey-hk2-2.27-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\hk2-locator\\2.5.0-b42\\hk2-locator-2.5.0-b42.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.5.0-b42/hk2-locator-2.5.0-b42-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.5.0-b42/hk2-locator-2.5.0-b42-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\external\\aopalliance-repackaged\\2.5.0-b42\\aopalliance-repackaged-2.5.0-b42.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.5.0-b42/aopalliance-repackaged-2.5.0-b42-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.5.0-b42/aopalliance-repackaged-2.5.0-b42-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\hk2-api\\2.5.0-b42\\hk2-api-2.5.0-b42.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.5.0-b42/hk2-api-2.5.0-b42-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.5.0-b42/hk2-api-2.5.0-b42-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\javax\\inject\\javax.inject\\1\\javax.inject-1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\hk2\\hk2-utils\\2.5.0-b42\\hk2-utils-2.5.0-b42.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.5.0-b42/hk2-utils-2.5.0-b42-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.5.0-b42/hk2-utils-2.5.0-b42-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\javassist\\javassist\\3.22.0-CR2\\javassist-3.22.0-CR2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/javassist/javassist/3.22.0-CR2/javassist-3.22.0-CR2-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\jersey\\media\\jersey-media-json-jackson\\2.27\\jersey-media-json-jackson-2.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-json-jackson/2.27/jersey-media-json-jackson-2.27-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-json-jackson/2.27/jersey-media-json-jackson-2.27-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\jersey\\ext\\jersey-entity-filtering\\2.27\\jersey-entity-filtering-2.27.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/ext/jersey-entity-filtering/2.27/jersey-entity-filtering-2.27-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/jersey/ext/jersey-entity-filtering/2.27/jersey-entity-filtering-2.27-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\module\\jackson-module-jaxb-annotations\\2.8.10\\jackson-module-jaxb-annotations-2.8.10.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.8.10/jackson-module-jaxb-annotations-2.8.10-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.8.10/jackson-module-jaxb-annotations-2.8.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\datatype\\jackson-datatype-jdk8\\2.9.5\\jackson-datatype-jdk8-2.9.5.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.5/jackson-datatype-jdk8-2.9.5-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.5/jackson-datatype-jdk8-2.9.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\datatype\\jackson-datatype-jsr310\\2.9.5\\jackson-datatype-jsr310-2.9.5.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.5/jackson-datatype-jsr310-2.9.5-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.5/jackson-datatype-jsr310-2.9.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\module\\jackson-module-parameter-names\\2.9.5\\jackson-module-parameter-names-2.9.5.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.5/jackson-module-parameter-names-2.9.5-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.5/jackson-module-parameter-names-2.9.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\tictactec\\ta-lib\\0.4.0\\ta-lib-0.4.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/tictactec/ta-lib/0.4.0/ta-lib-0.4.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/tictactec/ta-lib/0.4.0/ta-lib-0.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\java\\dev\\jna\\jna\\4.5.0\\jna-4.5.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/4.5.0/jna-4.5.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/4.5.0/jna-4.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\binance\\api\\binance-api-client\\1.0.1\\binance-api-client-1.0.1.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\logging\\log4j\\log4j-core\\2.13.3\\log4j-core-2.13.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.13.3/log4j-core-2.13.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-core/2.13.3/log4j-core-2.13.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\logging\\log4j\\log4j-api\\2.13.3\\log4j-api-2.13.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.13.3/log4j-api-2.13.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\logging\\log4j\\log4j-slf4j-impl\\2.13.3\\log4j-slf4j-impl-2.13.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.13.3/log4j-slf4j-impl-2.13.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-slf4j-impl/2.13.3/log4j-slf4j-impl-2.13.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\glassfish\\tyrus\\bundles\\tyrus-standalone-client-jdk\\1.17\\tyrus-standalone-client-jdk-1.17.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/tyrus/bundles/tyrus-standalone-client-jdk/1.17/tyrus-standalone-client-jdk-1.17-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/glassfish/tyrus/bundles/tyrus-standalone-client-jdk/1.17/tyrus-standalone-client-jdk-1.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\json\\json\\20200518\\json-20200518.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/json/json/20200518/json-20200518-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/json/json/20200518/json-20200518-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\bboxdb\\bboxdb-commons\\0.9.3\\bboxdb-commons-0.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/bboxdb/bboxdb-commons/0.9.3/bboxdb-commons-0.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/bboxdb/bboxdb-commons/0.9.3/bboxdb-commons-0.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\netty\\netty-all\\4.1.36.Final\\netty-all-4.1.36.Final.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.36.Final/netty-all-4.1.36.Final-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/netty/netty-all/4.1.36.Final/netty-all-4.1.36.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\okhttp3\\okhttp\\3.10.0\\okhttp-3.10.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.10.0/okhttp-3.10.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/3.10.0/okhttp-3.10.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\okio\\okio\\1.14.0\\okio-1.14.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.14.0/okio-1.14.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.14.0/okio-1.14.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\retrofit2\\retrofit\\2.3.0\\retrofit-2.3.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.3.0/retrofit-2.3.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/retrofit/2.3.0/retrofit-2.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\slf4j\\slf4j-log4j12\\1.8.0-beta2\\slf4j-log4j12-1.8.0-beta2.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-log4j12/1.8.0-beta2/slf4j-log4j12-1.8.0-beta2-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/slf4j/slf4j-log4j12/1.8.0-beta2/slf4j-log4j12-1.8.0-beta2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-context\\5.1.5.RELEASE\\spring-context-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-aop\\5.1.5.RELEASE\\spring-aop-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-beans\\5.1.5.RELEASE\\spring-beans-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-core\\5.1.5.RELEASE\\spring-core-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-jcl\\5.1.5.RELEASE\\spring-jcl-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-expression\\5.1.5.RELEASE\\spring-expression-5.1.5.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\retrofit2\\converter-scalars\\2.3.0\\converter-scalars-2.3.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-scalars/2.3.0/converter-scalars-2.3.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-scalars/2.3.0/converter-scalars-2.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\retrofit2\\converter-gson\\2.3.0\\converter-gson-2.3.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-gson/2.3.0/converter-gson-2.3.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/converter-gson/2.3.0/converter-gson-2.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\squareup\\retrofit2\\adapter-rxjava\\2.3.0\\adapter-rxjava-2.3.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava/2.3.0/adapter-rxjava-2.3.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/squareup/retrofit2/adapter-rxjava/2.3.0/adapter-rxjava-2.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\io\\reactivex\\rxjava\\1.3.0\\rxjava-1.3.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/reactivex/rxjava/1.3.0/rxjava-1.3.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/io/reactivex/rxjava/1.3.0/rxjava-1.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\alibaba\\fastjson\\1.2.12\\fastjson-1.2.12.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.12/fastjson-1.2.12-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.12/fastjson-1.2.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-lang3\\3.7\\commons-lang3-3.7.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-collections\\commons-collections\\3.2.1\\commons-collections-3.2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\ch\\qos\\logback\\logback-classic\\1.2.3\\logback-classic-1.2.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\ch\\qos\\logback\\logback-core\\1.2.3\\logback-core-1.2.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-codec\\commons-codec\\1.12\\commons-codec-1.12.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.12/commons-codec-1.12-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.12/commons-codec-1.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\commons\\commons-compress\\1.18\\commons-compress-1.18.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.18/commons-compress-1.18-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.18/commons-compress-1.18-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\junit\\junit\\4.12\\junit-4.12.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\projectlombok\\lombok\\1.18.4\\lombok-1.18.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.4/lombok-1.18.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.4/lombok-1.18.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-databind\\2.9.3\\jackson-databind-2.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.3/jackson-databind-2.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.3/jackson-databind-2.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-beanutils\\commons-beanutils\\1.9.3\\commons-beanutils-1.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-lang\\commons-lang\\2.6\\commons-lang-2.6.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\commons-logging\\commons-logging\\1.1.1\\commons-logging-1.1.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\sf\\ezmorph\\ezmorph\\1.0.6\\ezmorph-1.0.6.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/net/sf/ezmorph/ezmorph/1.0.6/ezmorph-1.0.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\net\\sf\\json-lib\\json-lib\\2.2.3\\json-lib-2.2.3-jdk15.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\spring-web\\5.0.9.RELEASE\\spring-web-5.0.9.RELEASE.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.9.RELEASE/spring-web-5.0.9.RELEASE-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.0.9.RELEASE/spring-web-5.0.9.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\jstl\\jstl\\1.2\\jstl-1.2.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\powermock\\powermock-api-mockito\\1.7.4\\powermock-api-mockito-1.7.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-mockito/1.7.4/powermock-api-mockito-1.7.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-mockito/1.7.4/powermock-api-mockito-1.7.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\powermock\\powermock-api-mockito-common\\1.7.4\\powermock-api-mockito-common-1.7.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-mockito-common/1.7.4/powermock-api-mockito-common-1.7.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-mockito-common/1.7.4/powermock-api-mockito-common-1.7.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\powermock\\powermock-api-support\\1.7.4\\powermock-api-support-1.7.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-support/1.7.4/powermock-api-support-1.7.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-api-support/1.7.4/powermock-api-support-1.7.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\powermock\\powermock-reflect\\1.7.4\\powermock-reflect-1.7.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-reflect/1.7.4/powermock-reflect-1.7.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-reflect/1.7.4/powermock-reflect-1.7.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\powermock\\powermock-core\\1.7.4\\powermock-core-1.7.4.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-core/1.7.4/powermock-core-1.7.4-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/powermock/powermock-core/1.7.4/powermock-core-1.7.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\mockito\\mockito-core\\1.10.19\\mockito-core-1.10.19.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\objenesis\\objenesis\\2.1\\objenesis-2.1.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\fasterxml\\jackson\\core\\jackson-core\\2.9.3\\jackson-core-2.9.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.3/jackson-core-2.9.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.3/jackson-core-2.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\com\\warrenstrange\\googleauth\\1.2.0\\googleauth-1.2.0.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/warrenstrange/googleauth/1.2.0/googleauth-1.2.0-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/com/warrenstrange/googleauth/1.2.0/googleauth-1.2.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpclient\\4.5.3\\httpclient-4.5.3.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "C:\\Users\\<USER>\\.m2\\repository\\org\\apache\\httpcomponents\\httpcore\\4.4.6\\httpcore-4.4.6.jar", "sourceContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.6/httpcore-4.4.6-sources.jar", "javadocContainerUrl": "file:/C:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.6/httpcore-4.4.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "C:\\eclipse-workspace\\gettrade\\src", "outputFolder": "C:\\eclipse-workspace\\gettrade\\target\\classes", "javadocContainerUrl": "file:/C:/eclipse-workspace/gettrade/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": true}, {"kind": "source", "path": "C:\\eclipse-workspace\\gettrade\\src\\main\\resources", "outputFolder": "C:\\eclipse-workspace\\gettrade\\target\\classes", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "C:\\eclipse-workspace\\gettrade\\src\\test\\resources", "outputFolder": "C:\\eclipse-workspace\\gettrade\\target\\test-classes", "isSystem": false, "isOwn": true, "isTest": true, "isJavaContent": false}, {"kind": "source", "path": "C:\\eclipse-workspace\\gettrade\\src\\test\\java", "outputFolder": "C:\\eclipse-workspace\\gettrade\\target\\test-classes", "javadocContainerUrl": "file:/C:/eclipse-workspace/gettrade/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}]}