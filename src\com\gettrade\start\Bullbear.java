package com.gettrade.start;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.domain.market.OrderBook;
import com.binance.api.client.domain.market.OrderBookEntry;
import com.binance.api.client.exception.BinanceApiException;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class Bullbear {

	public interface Trade extends Library {
		Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

		public void Trade_Bullbear();
		public void TradeStart(int nflag);
	}
	
	public static boolean btest=false;
	public static boolean init = false;
	public static int trade_flag = 0;

	public static int BUY_UP = 70001;
	public static int SELL_DOWN = 70002;
	
	public static int TIME_RESET = 4;
	public static int TRADE_STOP = 103;	
	
	public static int REFRESH = 700023;
	public static int REFRESH_CNT = 0;
	public static int REFRESH_CMP = 20;
	
	public static double volume = 0;
	public static double orderbook_S1 = 0.001;
	public static double orderbook_E1 = 1.5;
	public static double orderbook_S2 = 1.5;
	public static double orderbook_E2 = 1.5;	
	public static double trade_volume_max = 15;
	public static double trade_volume_min = 0;
	
	public static int get_orderbook_coin_M = 5;
	public static int start_time = 0;
	public static int end_time = 40;
	public static int Trace = 0;
	public static String symbol = "BTCUSDT";     
	public static int NextTradeSleep = 3000;
	
	public static BinanceSpotApiClientFactory factory = null;
	public static BinanceApiSpotRestClient client = null;
	public static BinanceFuturesApiClientFactory BinancefactoryF = null;
	public static BinanceApiFuturesRestClient clientF = null;

	public static void main(String[] args) throws InterruptedException, IOException {

		System.setProperty("https.proxyHost", "*************");
		System.setProperty("https.proxyPort", "8088");
		
		factory = BinanceAbstractFactory.createSpotFactory("", "");
		client = factory.newRestClient();
		
		BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
		clientF = BinancefactoryF.newRestClient();
	    
		if(!btest) {
			Trade.INSTANCE.Trade_Bullbear();
			Trade.INSTANCE.TradeStart(TIME_RESET);
		}

		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//Trade.INSTANCE.TradeStart(1);
		        	
		SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.KOREA);
		
        ScheduledExecutorService serviceGetData = Executors.newSingleThreadScheduledExecutor();
        AtomicBoolean isRunning = new AtomicBoolean(false);

        Runnable runnableGetData = new Runnable() {
            @Override
            public void run() {
            	try {
            		if (isRunning.compareAndSet(false, true)) {
	            	 	
		    			long totalMilliseconds = System.currentTimeMillis();
		    			long currentSecond = (totalMilliseconds/ 1000) % 60;
		    			if (!(currentSecond >= start_time && currentSecond <= end_time) )  {
		    				
		        			if(currentSecond >= 58 && currentSecond <= 59 && !init) {
		        				Trade.INSTANCE.TradeStart(TIME_RESET);
		        				init = true;
		    		   		    try {
		    		   		    	List<Candlestick> candlestickList = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20, null, null);		    		   		        
		    		   		     
		    		   		    	Candlestick candlestick = null;
		    		   		        List<Double> priceList = new ArrayList<>();
		    		    			for (int i = 0; i < candlestickList.size(); i++) {
		    		    			    candlestick = candlestickList.get(i);
		    		    				priceList.add(Double.parseDouble(candlestick.getClose()));
		    		    				if( i >= 15)
		    		    					volume += Double.valueOf(candlestick.getVolume()).doubleValue();
		    		    			}		
		    		    			volume = volume / 5;
		    			    					    		    					    		    			
		    		    			String filePath = "c:/Trade/Trade_bullbear.properties";
		    		    			InputStream input = new FileInputStream(filePath);
	
	    			   	            Properties prop = new Properties();
		    		    	        prop.load(input);
		    			    	    	
		    		    	    	orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1","0.001"));
		    		    	    	orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1","1"));
		    		    	    	orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2","4"));
		    		    	    	orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2","10"));
		    		    	        get_orderbook_coin_M = Integer.parseInt(prop.getProperty("get_orderbook_coin_M","5"));
		    		    	        trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max","40"));
		    		    	        trade_volume_min = Integer.parseInt(prop.getProperty("trade_volume_min","20"));
		    		    	        start_time = Integer.parseInt(prop.getProperty("start_time","0"));
		    		    	        end_time = Integer.parseInt(prop.getProperty("end_time","40"));
		    		    	        Trace = Integer.parseInt(prop.getProperty("Trace","0"));
		    		    	        NextTradeSleep = Integer.parseInt(prop.getProperty("NextTradeSleep","5000"));
		    		    	        
		    		    	        input.close();
			    		    	    System.out.println("orderbook_S1:"+orderbook_S1 );
			    		    	    System.out.println("orderbook_E1:"+orderbook_E1 );
			    		    	    System.out.println("orderbook_S2:"+orderbook_S2 );
			    		    	    System.out.println("orderbook_E2:"+orderbook_E2 );
			    		    	    System.out.println("get_orderbook_coin_M:"+get_orderbook_coin_M );
			    		    	    System.out.println("trade_volume_max:"+trade_volume_max );
			    		    	    System.out.println("trade_volume_min:"+trade_volume_min );
			    		    	    System.out.println("start_time:"+start_time );
			    		    	    System.out.println("end_time:"+end_time );
			    		    	    System.out.println("Trace:"+Trace );
			    		    	    System.out.println("volume:"+volume );
			    		    	    System.out.println("NextTradeSleep:"+NextTradeSleep );

			    		    	    System.out.println("REFRESH_CNT:"+REFRESH_CNT );
			    		    	    System.out.println("REFRESH_CMP:"+REFRESH_CMP );
			    		    	    
		    		   		    } catch (BinanceApiException e) {
		    		   		        System.out.println("Failed to get candlestick data: " + e.getMessage());
		    		   		        volume = 0;
		    		   		        return;
		    		   		    }
		        			}
		        				
		                	if(REFRESH_CNT >=REFRESH_CMP ) {
		                		Random random = new Random();
		                		REFRESH_CMP = random.nextInt(10) + 10;
		                		Trade.INSTANCE.TradeStart(REFRESH);
		                		Thread.sleep(1000);
		                		Trade.INSTANCE.TradeStart(REFRESH);
		                		REFRESH_CNT=0;
		                	}
		                	REFRESH_CNT++;
	        				trade_flag = 0;
		    				return;
		    			}
				    			
		    			init = false;  
		    			if(currentSecond < start_time || trade_flag != 0 || !(volume > trade_volume_min && volume < trade_volume_max)) {
		    				if(Trace == 1)
		    					System.out.println("PASS currentSecond:"+currentSecond+" trade_flag:"+trade_flag+" volume:"+volume+" trade_volume_min:"+trade_volume_min+" trade_volume_max:"+trade_volume_max);
		    				return;
		    			}
		    			
		    		   	double trade_orderbook_down_F = 0;
		    		   	double trade_orderbook_up_F = 0;
		    		   	double trade_orderbook_down = 0;
		    		   	double trade_orderbook_up = 0;
		    		   	
		    		    // Get the order book data
		    		    OrderBook orderBookF = clientF.getOrderBook(symbol, get_orderbook_coin_M);
		    		        
		    		    List<OrderBookEntry> bidsF = orderBookF.getBids();
		    		    List<OrderBookEntry> asksF = orderBookF.getAsks();
		    		        
		    		    for(int i=0; i < bidsF.size(); i++) {
		    		    	trade_orderbook_down_F += Double.parseDouble(bidsF.get(i).getQty());
		    		    }
		    		        
		    		    for(int i=0; i < asksF.size(); i++) {
		    		    	trade_orderbook_up_F += Double.parseDouble(asksF.get(i).getQty());
		    		    }

		    		    // Get the order book data
		    		    OrderBook orderBook = client.getOrderBook(symbol, get_orderbook_coin_M);
		    		        
		    		    List<OrderBookEntry> bids = orderBook.getBids();
		    		    List<OrderBookEntry> asks = orderBook.getAsks();
		    		        
		    		    for(int i=0; i < bids.size(); i++) {
		    		    	trade_orderbook_down += Double.parseDouble(bids.get(i).getQty());
		    		    }
		    		        
		    		    for(int i=0; i < asks.size(); i++) {
		    		    	trade_orderbook_up += Double.parseDouble(asks.get(i).getQty());
		    		    }
		    		    
		    		    if(Trace == 1)
		    		      	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down+" trade_orderbook_up_F:"+trade_orderbook_up_F+" trade_orderbook_down_F:"+trade_orderbook_down_F);
		    		        		    		 	
		    		   	if( trade_flag == 0 && (orderbook_S1 < trade_orderbook_up && trade_orderbook_up < orderbook_E1) && (orderbook_S2 < trade_orderbook_down && trade_orderbook_down < orderbook_E2) &&
		        	   		(orderbook_S1 < trade_orderbook_up_F && trade_orderbook_up_F < orderbook_E1) && (orderbook_S2 < trade_orderbook_down_F && trade_orderbook_down_F < orderbook_E2) ) {
			   		   			trade_flag = 1;
				               	if(!btest ) {
				               		Trade.INSTANCE.TradeStart(BUY_UP);
				               		Thread.sleep(NextTradeSleep);
				               	}
				               				
				               	String str = timeFormat.format(new Date(totalMilliseconds));
				           	   	System.out.println("Trade Buy case S1: " + str);
				              			    		    				
				               	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down+" trade_orderbook_up_F:"+trade_orderbook_up_F+" trade_orderbook_down_F:"+trade_orderbook_down_F);
		    		   	}else if(trade_flag == 0 &&(orderbook_S1 < trade_orderbook_down && trade_orderbook_down < orderbook_E1) && (orderbook_S2 < trade_orderbook_up && trade_orderbook_up < orderbook_E2) &&
		    		   		(orderbook_S1 < trade_orderbook_down_F && trade_orderbook_down_F < orderbook_E1) && (orderbook_S2 < trade_orderbook_up_F && trade_orderbook_up_F < orderbook_E2)) {
		    		   			trade_flag = 2;
					          	if(!btest ) {
				               		Trade.INSTANCE.TradeStart(SELL_DOWN);
				               		Thread.sleep(NextTradeSleep);
					          	}
				               	String str = timeFormat.format(new Date(totalMilliseconds));
				           		System.out.println("Trade Sell case S1: " + str);
				                			    	    				
				           		System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down+" trade_orderbook_up_F:"+trade_orderbook_up_F+" trade_orderbook_down_F:"+trade_orderbook_down_F);

		    		   	}else if( trade_flag == 2 && (orderbook_S1 < trade_orderbook_up && trade_orderbook_up < orderbook_E1) && (orderbook_S2 < trade_orderbook_down && trade_orderbook_down < orderbook_E2) &&
			        	  	(orderbook_S1 < trade_orderbook_up_F && trade_orderbook_up_F < orderbook_E1) && (orderbook_S2 < trade_orderbook_down_F && trade_orderbook_down_F < orderbook_E2) ) {
				   		  		trade_flag = 1;
					           	if(!btest ) {
					           		Trade.INSTANCE.TradeStart(BUY_UP);
					           		Thread.sleep(NextTradeSleep);
					           	}
					               				
					           	String str = timeFormat.format(new Date(totalMilliseconds));
					       	   	System.out.println("Trade Buy case S2: " + str);
					              			    		    				
					           	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down+" trade_orderbook_up_F:"+trade_orderbook_up_F+" trade_orderbook_down_F:"+trade_orderbook_down_F);
			    	   	}else if(trade_flag == 1 && (orderbook_S1 < trade_orderbook_down && trade_orderbook_down < orderbook_E1) && (orderbook_S2 < trade_orderbook_up && trade_orderbook_up < orderbook_E2) &&
			    		   	(orderbook_S1 < trade_orderbook_down_F && trade_orderbook_down_F < orderbook_E1) && (orderbook_S2 < trade_orderbook_up_F && trade_orderbook_up_F < orderbook_E2)) {
				    	   		trade_flag = 2;
							   	if(!btest ) {
						       		Trade.INSTANCE.TradeStart(SELL_DOWN);
						       		Thread.sleep(NextTradeSleep);
							   	}
						       	String str = timeFormat.format(new Date(totalMilliseconds));
						       	System.out.println("Trade Sell case S2: " + str);
						                			    	    				
						       	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down+" trade_orderbook_up_F:"+trade_orderbook_up_F+" trade_orderbook_down_F:"+trade_orderbook_down_F);
			    	   	}
			    	    
		            }
                }catch (Exception e) {
                    e.printStackTrace();
                    System.out.println(e.getMessage());
                }finally {
                    isRunning.set(false);
                }
            }
       };

       serviceGetData.scheduleAtFixedRate(runnableGetData, 0, 75, TimeUnit.MILLISECONDS);
        
		while (true) {
			try {
				Thread.sleep(300000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
    
}