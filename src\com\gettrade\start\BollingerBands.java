package com.gettrade.start;

import java.util.ArrayList;
import java.util.List;

public class BollingerBands {
    private List<Double> upperBands = new ArrayList<>();
    private List<Double> lowerBands = new ArrayList<>();
    private List<Double> middleBands = new ArrayList<>();

    public BollingerBands(List<Double> prices, int period, double stdDevFactor) {
        for (int i = period - 1; i < prices.size(); i++) {
            List<Double> subset = prices.subList(i - period + 1, i + 1);
            calculateBollingerBands(subset, stdDevFactor);
        }
    }

    private void calculateBollingerBands(List<Double> prices, double stdDevFactor) {
        double sma = calculateSMA(prices);
        double stdDev = calculateStdDev(prices, sma);

        double upperBand = sma + stdDevFactor * stdDev;
        double lowerBand = sma - stdDevFactor * stdDev;

        this.upperBands.add(upperBand);
        this.lowerBands.add(lowerBand);
        this.middleBands.add(sma);
    }

    private double calculateSMA(List<Double> prices) {
        double sum = 0.0;
        for (double price : prices) {
            sum += price;
        }
        return sum / prices.size();
    }

    private double calculateStdDev(List<Double> prices, double sma) {
        double sum = 0.0;
        for (double price : prices) {
            sum += Math.pow(price - sma, 2.0);
        }
        double variance = sum / (prices.size() - 1); // 수정: n -> n-1
        return Math.sqrt(variance);
    }
    
    public double getUpBollingerBands() {
    
    	return upperBands.get(upperBands.size()-1).doubleValue();
    }
    
    public double getdownBollingerBands() {
        
    	return lowerBands.get(lowerBands.size()-1).doubleValue();
    }
    
    public double getMidBollingerBands() {
        
    	return middleBands.get(middleBands.size()-1).doubleValue();
    }
    
    public List<Double> getUpBollingerBandsList() {
    	return upperBands;
    }
    
    public List<Double> getMidBollingerBandsList() {
    	return middleBands;
    }
    
    public List<Double> getDnBollingerBandsList() {
    	return lowerBands;
    }
}
