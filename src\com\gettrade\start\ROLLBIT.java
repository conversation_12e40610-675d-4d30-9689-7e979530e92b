package com.gettrade.start;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class ROLLBIT {

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_bcgame();
        public void TradeStart(int nflag, String nValue);
        public void SetPnLData();
        public double GetPnLData();
        public double GetBCUpDownPriceRec();
        public double GetTradeEntry();
        public void SetTradeEntry();
        public String GetTrade_Data();
        public void DelData();
    }
    
    public static int BUY_UP = 600001;
    public static int SELL_DOWN = 600002;
    public static int PLACEBET = 600003;
    public static int CLOSEBET = 600004;
    public static int GETPNL = 600005;
    public static int GETPRICE = 600006;
    public static int MSGCLICK = 600007;
    public static int GETENTRY = 600008;
    public static int TRADEDATA = 600009;
    public static int OPENDATA = 600010;
    public static int WINDOWS_TOP = 300008;
    
    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;    
    public static int REFRESH_CNT = 0;
    public static double volume = 0;
    public static double volumeBTC = 0;
    public static double orderbook_S1 = 0.001;
    public static double orderbook_E1 = 1.5;
    public static double orderbook_S2 = 1.5;
    public static double orderbook_E2 = 1.5;
    public static double volumeBTC_Check = 15;
    public static double trade_volume_max = 15;
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;
    public static int BollCheck = 0;
    public static int checkboll = 0;
    public static int TradeFlg = 0;
    public static int INIT_WAIT = 0;
    public static int TRACE = 0;
    public static int STOP_TRADE = 0;
    
    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;
                    
    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false);
    private static AtomicBoolean first = new AtomicBoolean(false);
    
    public static String symbol = "BTCUSDT";
    
    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();
    public static void asyncPrint(String message) {
    	PrintService.submit(() -> System.out.println(message));
    }
            
    public static void TradeStart(int nflag) {
    	Trade.INSTANCE.TradeStart(nflag, ""); // Default value for nValue is 0
    }

    public static void TradeStart(int nflag, String nValue) {
    	Trade.INSTANCE.TradeStart(nflag, nValue);
    }
    
    static class MessageHandler implements Runnable {
        private boolean bfirstBTC20 = false;
        private boolean bfirstFBTC20 = false;
        
        private double trade_orderbook_down_F20 = 99;
        private double trade_orderbook_up_F20 = 99;
        private double trade_orderbook_down20 = 99;
        private double trade_orderbook_up20 = 99;     
        private String[] orderbook20MSg = new String[2];
        private Map<String, Double> previousPrices = new HashMap<>();
        private Map<String, Double> increasedPrices = new HashMap<>();
        private Map<String, Double> decreasedPrices = new HashMap<>();
        
        @Override
        public void run() {
			//messageProcessingService.submit(() -> {
				processCheck();
			//});	
        	
			/*
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    while (true) {
						String message = in.readLine();

                    	if(TradeFlg > 0 && TradeFlg <= 3) {
                    		processTrade(message);
                    	}else {
	                        if (message != null) {
	        					long totalMilliseconds = System.currentTimeMillis();
	        					long currentSecond = (totalMilliseconds / 1000) % 60;
	
	        					if ((currentSecond >= 59 && currentSecond <= 59) || !first.get()) {
	        						if (initTrade.compareAndSet(false, true)) {
		        						messageProcessingService.submit(() -> {
		        						    InitMain();
		        						    first.set(true);
		        						    try {
		        						        Thread.sleep(1100);
		        						    } catch (InterruptedException e) {
		        						        e.printStackTrace();
		        						    }
		        						    resetTradeOrderbook();
		        						    resetFlags();
		        						    initTrade.set(false);
		        						});
	        						}
	        					}else {
	        						if (initTrade.compareAndSet(false, true)) {
			                   	        processMessage(message);
			                   	        initTrade.set(false);
	        						}
	        					}
	                       	//}
	                        }
                    	}
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    resetTradeOrderbook();
                    resetFlags();
                    initTrade.set(false);
                    try {
                        Thread.sleep(5000); // Wait 5 seconds before attempting to reconnect
                    } catch (InterruptedException ie) {
                        ie.printStackTrace();
                    }
                }
            }
            */
        }
   
        private void processTrade(String message) {
            if (message != null) {
                // Process the message based on its type
                JsonObject jsonObject = JsonParser.parseString(message).getAsJsonObject();
                if (jsonObject.has("Mtype") && jsonObject.has("message")) {    
                    String mtype = jsonObject.get("Mtype").getAsString();
                    if ("BTC20".equals(mtype)) {
                        bfirstBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        orderbook20MSg = msg.split("-");
                        trade_orderbook_down20 = Double.parseDouble(orderbook20MSg[0]);
                        trade_orderbook_up20 = Double.parseDouble(orderbook20MSg[1]);
                    }else if ("FBTC20".equals(mtype)) {
                        bfirstFBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        orderbook20MSg = msg.split("-");
                        trade_orderbook_down_F20 = Double.parseDouble(orderbook20MSg[0]);
                        trade_orderbook_up_F20 = Double.parseDouble(orderbook20MSg[1]);
                    }

                    if( bfirstBTC20 && bfirstFBTC20 /*&& bfirstBBTC20 && bfirstBFBTC20*/) {

                    	TradeStart(GETENTRY);
						double CloseData = Trade.INSTANCE.GetTradeEntry();
                 
						/*
                    	if(TradeFlg == 1 || TradeFlg == 2) {
                            if(1 < trade_orderbook_up20 && 1 < trade_orderbook_down20 && 1 < trade_orderbook_up_F20 && 1 < trade_orderbook_down_F20) {
                            	TradeStart(CLOSEBET);
                            	TradeFlg=0;
                            	asyncPrint("CLOSEBET INIT");
                            }
                    	}
                    	*/
						
			        	if(TradeFlg == 1) {
			                if( CloseData > 0 && 
			                		( (1 < trade_orderbook_up20 && 1 < trade_orderbook_down20 && 1 < trade_orderbook_up_F20 && 1 < trade_orderbook_down_F20) || (1 < trade_orderbook_up20 && 1 < trade_orderbook_up_F20 && (1 > trade_orderbook_down20 || 1 > trade_orderbook_down_F20)) )) {
			                		 // (1 < trade_orderbook_up_B20 && 1 < trade_orderbook_down_B20 && 1 < trade_orderbook_up_BF20 && 1 < trade_orderbook_down_BF20) || (1 < trade_orderbook_up_B20 && 1 < trade_orderbook_up_F20 && (1 > trade_orderbook_down_B20 || 1 > trade_orderbook_down_BF20))	)) {
			      		        TradeStart(CLOSEBET);
			      		        asyncPrint("up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20+" CloseData:"+CloseData);
			      		        TradeFlg=3;
			             	}
			            } else if (TradeFlg == 2 ) {
			                if( CloseData > 0 && 
			                		( (1 < trade_orderbook_up20 && 1 < trade_orderbook_down20 && 1 < trade_orderbook_up_F20 && 1 < trade_orderbook_down_F20) || (1 < trade_orderbook_down20 && 1 < trade_orderbook_down_F20 && (1 > trade_orderbook_up20 || 1 > trade_orderbook_up_F20 )) )) {
			                         // (1 < trade_orderbook_up_B20 && 1 < trade_orderbook_down_B20 && 1 < trade_orderbook_up_BF20 && 1 < trade_orderbook_down_BF20) || (1 < trade_orderbook_down_B20 && 1 < trade_orderbook_down_BF20 && (1 > trade_orderbook_up_B20 || 1 > trade_orderbook_up_BF20 )) )) {

								TradeStart(CLOSEBET);
								asyncPrint("up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20+" CloseData:"+CloseData);
								TradeFlg=3;
							}
						} else if (TradeFlg == 3 ) {
                            if(1 < trade_orderbook_up20 && 1 < trade_orderbook_down20 && 1 < trade_orderbook_up_F20 && 1 < trade_orderbook_down_F20 /*&& 1 < trade_orderbook_up_B20 && 1 < trade_orderbook_down_B20 && 1 < trade_orderbook_up_BF20 && 1 < trade_orderbook_down_BF20*/) {
                            	asyncPrint("CLOSEBET INIT 3");
                            	TradeFlg=0;
    		                    resetTradeOrderbook();
    		                    resetFlags();	
                            }
                            if(CloseData > 0)
                            	TradeStart(CLOSEBET);
						}
                    	
			        	if(TRACE == 1)
			        		asyncPrint("up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20+" CloseData:"+CloseData);
                	}
                }
            }
        }
       
        private void processMessage(String message) {
            if(TradeFlg > 2) {
                resetTradeOrderbook();
                resetFlags();
                return;
            }
            if (message != null) {
                // Process the message based on its type
                JsonObject jsonObject = JsonParser.parseString(message).getAsJsonObject();
                if (jsonObject.has("Mtype") && jsonObject.has("message")) {    
                    String mtype = jsonObject.get("Mtype").getAsString();
                    if ("BTC20".equals(mtype)) {
                        bfirstBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        orderbook20MSg = msg.split("-");
                        trade_orderbook_down20 = Double.parseDouble(orderbook20MSg[0]);
                        trade_orderbook_up20 = Double.parseDouble(orderbook20MSg[1]);
                    }else if ("FBTC20".equals(mtype)) {
                        bfirstFBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        orderbook20MSg = msg.split("-");
                        trade_orderbook_down_F20 = Double.parseDouble(orderbook20MSg[0]);
                        trade_orderbook_up_F20 = Double.parseDouble(orderbook20MSg[1]);
                    }
                    		
                    if(BollCheck == 1 && checkboll == 1) {
                    	asyncPrint("NOT TRADE");
                        TradeFlg = 4;
                        return;
                    }
                        
                    if( volume <= trade_volume_max && /*volumeBTC > 10 &&*/ volumeBTC <= volumeBTC_Check ) {
                    	
                    	if( bfirstBTC20 && bfirstFBTC20 && /*bfirstBBTC20 && bfirstBFBTC20 &&*/
                    			trade_orderbook_up20 != 99 && trade_orderbook_down20 != 99 && trade_orderbook_up_F20 != 99 && trade_orderbook_down_F20 != 99 ){
                    			//trade_orderbook_up_B20 != 99 && trade_orderbook_down_B20 != 99 && trade_orderbook_up_BF20 != 99 && trade_orderbook_down_BF20 != 99) {

                        	if(TradeFlg == 0 && 
                        	    ((orderbook_S1 < trade_orderbook_up20 && trade_orderbook_up20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_down20 && trade_orderbook_down20 < orderbook_E2)) && 
                        		((orderbook_S1 < trade_orderbook_up_F20 && trade_orderbook_up_F20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_down_F20 && trade_orderbook_down_F20 < orderbook_E2))) {
                        	   // ((orderbook_S1 < trade_orderbook_up_B20 && trade_orderbook_up_B20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_down_B20 && trade_orderbook_down_B20 < orderbook_E2)) && 
                        		//((orderbook_S1 < trade_orderbook_up_BF20 && trade_orderbook_up_BF20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_down_BF20 && trade_orderbook_down_BF20 < orderbook_E2))) {

                        		String maxChangeCoin = null;
                        		synchronized (this) {
	                        		double maxChangePercentage = 0.0;
	                        		
	                        		for (Map.Entry<String, Double> entry : decreasedPrices.entrySet()) {
	                        		    String coin = entry.getKey();
	                        		    double previousPrice = previousPrices.get(coin);
	                        		    double currentPrice = entry.getValue();
	                        		    double changePercentage = Math.abs(currentPrice - previousPrice) / previousPrice;
	
	                           		    if (changePercentage > maxChangePercentage) {
	                        		        maxChangePercentage = changePercentage;
	                        		        maxChangeCoin = coin;
	                        		    }
	                        		}
	
	                        		asyncPrint(maxChangeCoin + ": " + decreasedPrices.get(maxChangeCoin) + " (BUY Change: " + (maxChangePercentage * 100) + "%)");
                        		}
                                if(INIT_WAIT > 0 && maxChangeCoin != null && !maxChangeCoin.equals("Rollercoaster")) {
							       TradeFlg = 1;
                                   TradeStart(BUY_UP,maxChangeCoin);
                                   /*
                             	   try {
								       Thread.sleep(10);
								       //TradeStart(PLACEBET);
								   } catch (InterruptedException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
								   }
								   */
                             	   Trade.INSTANCE.SetTradeEntry();
                             	   Trade.INSTANCE.SetPnLData();
                             	   asyncPrint("BINAINCE Trade Buy volume:"+volume+" volumeBTC:"+volumeBTC+" up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20);

                                }
								if (maxChangeCoin == null) {
									TradeStart(OPENDATA);
								}
								
                            }else if(TradeFlg == 0 && 
                            		((orderbook_S1 < trade_orderbook_down20 && trade_orderbook_down20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_up20 && trade_orderbook_up20 < orderbook_E2)) &&
                            		((orderbook_S1 < trade_orderbook_down_F20 && trade_orderbook_down_F20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_up_F20 && trade_orderbook_up_F20 < orderbook_E2)) ) {
                            		//((orderbook_S1 < trade_orderbook_down_B20 && trade_orderbook_down_B20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_up_B20 && trade_orderbook_up_B20 < orderbook_E2)) &&
                            		//((orderbook_S1 < trade_orderbook_down_BF20 && trade_orderbook_down_BF20 < orderbook_E1) && (orderbook_S2 < trade_orderbook_up_BF20 && trade_orderbook_up_BF20 < orderbook_E2))) {
                            		
                            	String maxChangeCoin = null;
                            	synchronized (this) {
                            	    //previousPrices = getPreviousPrices();
                            	    //increasedPrices = getIncreasedCoins();
	                        		double maxChangePercentage = 0.0;
	                        		for (Map.Entry<String, Double> entry : increasedPrices.entrySet()) {
	                        		    String coin = entry.getKey();
	                        		    double previousPrice = previousPrices.get(coin);
	                        		    double currentPrice = entry.getValue();
	                        		    double changePercentage = Math.abs(currentPrice - previousPrice) / previousPrice;
	
	                        		    if (changePercentage > maxChangePercentage) {
	                        		        maxChangePercentage = changePercentage;
	                        		        maxChangeCoin = coin;
	                        		    }
	                        		}
	
	                        		asyncPrint(maxChangeCoin + ": " + increasedPrices.get(maxChangeCoin) + " (SELL Change: " + (maxChangePercentage * 100) + "%)");
                            	}
                               	if(INIT_WAIT > 0 && maxChangeCoin != null && !maxChangeCoin.equals("Rollercoaster")) {
								    TradeFlg = 2;
								    TradeStart(SELL_DOWN,maxChangeCoin);
								    /*
                              	    try {
 								        Thread.sleep(10);
 								        //TradeStart(PLACEBET);
 								    } catch (InterruptedException e) {
 										// TODO Auto-generated catch block
 										e.printStackTrace();
 								    }
								    */
                              	    Trade.INSTANCE.SetTradeEntry();
                              	    Trade.INSTANCE.SetPnLData();
                              	    asyncPrint("BINAINCE Trade Sell volume:"+volume+" volumeBTC:"+volumeBTC+" up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20);

                                }
                               	
								if (maxChangeCoin == null) {
									TradeStart(OPENDATA);
								}
                             }
                    	 }
                    }
                    
                    if(TRACE == 1) {
                        asyncPrint("TRACE volume:"+volume+" volumeBTC:"+volumeBTC +" up20:"+trade_orderbook_up20+" dn20:"+trade_orderbook_down20 +" up_F20:"+trade_orderbook_up_F20+" dn_F20:"+trade_orderbook_down_F20);
                    }
                    
                }
            }    	
        } 
        
        private void processCheck() {
        	while(true) {
	        	TradeStart(TRADEDATA);
	        	String info = Trade.INSTANCE.GetTrade_Data();
	        	int priceIncreasedCount =0;
	        	int priceDecreasedCount =0;
	        	int priceUnchangedCount =0;
	        	
				if (info != null) {
					info = info.replaceAll("^\"|\"$", "").replace("\\", "").trim();
	            	if (info.startsWith("[") && info.endsWith("]")) {
	            	    JsonArray jsonArray = JsonParser.parseString(info).getAsJsonArray();
	            	    synchronized (this) {
		                    for (JsonElement element : jsonArray) {
		                        JsonObject jsonObject = element.getAsJsonObject();
		                        String name = jsonObject.get("css1sf65qc").getAsString();
		                        if(!name.equals("Rollercoaster")) {
			                        double price = Double.parseDouble(jsonObject.get("cssu71o44").getAsString().replace(",", "").trim());
			                        if (previousPrices.containsKey(name)) {
			                            double previousPrice = previousPrices.get(name);
			                            if (price > previousPrice) {
			                                increasedPrices.put(name, price);
			                                priceIncreasedCount++;
			                            } else if (price < previousPrice) {
			                                decreasedPrices.put(name, price);
			                                priceDecreasedCount++;
			                            }else {
                                            priceUnchangedCount++;
			                            }
			                        }
	
			                        previousPrices.put(name, price);
		                        }
		                    }
	            	    }
	    			}
	            	Trade.INSTANCE.DelData();
	            	int total = priceIncreasedCount + priceDecreasedCount + priceUnchangedCount;

	            	if (total != 0 && (priceIncreasedCount >= 0.7 * total || priceDecreasedCount >= 0.7 * total)) {
	            	    asyncPrint("Price increased count: " + priceIncreasedCount +
	            	               ", Price decreased count: " + priceDecreasedCount +
	            	               ", Price unchanged count: " + priceUnchangedCount +
	            	               ", Total: " + total);
	            	}
				}

				try {
					Thread.sleep(10);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
        	}
        }
        
        private void resetTradeOrderbook() {
            trade_orderbook_down_F20 = 99;
            trade_orderbook_up_F20 = 99;
            trade_orderbook_down20 = 99;
            trade_orderbook_up20 = 99;

        }

        private void resetFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        public synchronized Map<String, Double> getPreviousPrices() {
            return new HashMap<>(previousPrices);
        }
        
        public synchronized Map<String, Double> getIncreasedCoins() {
            return new HashMap<>(increasedPrices);
        }
        
        public synchronized Map<String, Double> getDecreasedCoins() {
            return new HashMap<>(decreasedPrices);
        }
    }
    
    
    public static void InitMain() {

            try {         
                if(REFRESH_CNT > 10 ) {
                    TradeStart(REFRESH);
                    TradeStart(TIME_RESET);
                	Thread.sleep(10000);
                	TradeStart(MSGCLICK);
                	TradeStart(OPENDATA);
                    REFRESH_CNT = 0;
                }
                REFRESH_CNT++;
            }catch (InterruptedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
               try {
                   volume = 0;
                   List<Candlestick> candlestickList = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20, null, null);                                       
                
                   Candlestick candlestick = null;
                   List<Double> priceList = new ArrayList<>();
	                for (int i = 0; i < candlestickList.size(); i++) {
	                	candlestick = candlestickList.get(i);
	                    if( i >= 9 && i < candlestickList.size()-1) 
	                        volume += Double.valueOf(candlestick.getVolume()).doubleValue();
	     
	                    priceList.add(Double.parseDouble(candlestick.getClose()));
	                }        
	                volume = volume / 10;
	                volume = roundToTwoDecimalPlaces(volume);
	
	                BollingerBands boll = new BollingerBands(priceList, 20, 2.0);
	                BollDown = boll.getdownBollingerBands();
	                BollUp = boll.getUpBollingerBands();
	                
	                if( Double.parseDouble(candlestick.getHigh()) > BollUp || Double.parseDouble(candlestick.getLow()) < BollDown )
	                    BollCheck = 1;
	                else
	                    BollCheck = 0;
	                
	                volumeBTC = 0;
	                candlestickList = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20, null, null);                                       
	                
	                for (int i = 0; i < candlestickList.size(); i++) {
	                    candlestick = candlestickList.get(i);
	                    if( i >= 9 && i < candlestickList.size()-1) 
	                        volumeBTC += Double.valueOf(candlestick.getVolume()).doubleValue();
	                }        
	                
	                volumeBTC = volumeBTC / 10;
	                volumeBTC = roundToTwoDecimalPlaces(volumeBTC);
	 
	                        
	                String filePath = "";
	                filePath = "c:/Trade/TradeRollbit.properties";
	                
	                InputStream input = new FileInputStream(filePath);
	                Properties prop = new Properties();
					prop.load(input);

	                orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1","0.001"));
	                orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1","1"));
	                orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2","4"));
	                orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2","10"));
	                
	                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check","15"));
	                trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max","15"));
	                checkboll = Integer.parseInt(prop.getProperty("checkboll","0"));
	                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT","0"));
	                TRACE = Integer.parseInt(prop.getProperty("TRACE","0"));
	                STOP_TRADE = Integer.parseInt(prop.getProperty("STOP_TRADE","0"));
	                
	                asyncPrint("orderbook_S1:"+orderbook_S1 );
	                asyncPrint("orderbook_E1:"+orderbook_E1 );
	                asyncPrint("orderbook_S2:"+orderbook_S2 );
	                asyncPrint("orderbook_E2:"+orderbook_E2 );
	                
	                asyncPrint("volumeBTC_Check:"+volumeBTC_Check );
	                asyncPrint("trade_volume_max:"+trade_volume_max );
	                asyncPrint("BollUp:"+BollUp );
	                asyncPrint("BollDown:"+BollDown );
	                asyncPrint("volume:"+volume );
	                asyncPrint("volumeBTC:"+volumeBTC );
	                asyncPrint("checkboll:"+checkboll );
	                asyncPrint("INIT_WAIT:"+INIT_WAIT );
	                asyncPrint("TRACE:"+TRACE );
	                asyncPrint("STOP_TRADE:"+STOP_TRADE );
	                
                
               } catch (IOException e) {
                   asyncPrint("Failed to get candlestick data: " + e.getMessage());
                   volume = 0;
                   return;
               }
 
               TradeFlg = 5;
               double price = 0;
				for (int i = 0; i < 10; i++) {
					TradeStart(GETPNL);
		            TradeStart(GETPRICE);
					try {
						Thread.sleep(1000);
						price = Trade.INSTANCE.GetBCUpDownPriceRec();
						if(price != 0.0) {
							if (price > STOP_TRADE) {
								TradeFlg = 0; 
								break;
							} 
						}
						asyncPrint("Check TradeFlg:"+TradeFlg+" price:"+price );
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				} 
				asyncPrint("Check TradeFlg:"+TradeFlg+" price:"+price );
                TradeStart(GETPNL);
				if (Trade.INSTANCE.GetPnLData() != 0.0) {
					TradeStart(CLOSEBET);
				}
    }
    
    public static void main(String[] args) throws InterruptedException, IOException {
        
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();
        
        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();
        
        
        Trade.INSTANCE.TradeBitqDlg_bcgame();
                               
        TradeStart(WINDOWS_TOP);
        TradeStart(TIME_RESET);
        //SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.KOREA);

        
        try {
            Thread.sleep(15000);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        //Trade.INSTANCE.TradeStart(1);
        
        Thread thread = new Thread(new MessageHandler());
        thread.start();
        
        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df = new DecimalFormat("#.####");
        return Double.parseDouble(df.format(value));
    }
    
}