package com.gettrade.start;

import java.io.BufferedWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.IntStream;

import org.json.JSONArray;
import org.json.JSONObject;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

public class GetOrderBookSrv {

    public static double volumeBTC = 100;

    public static String symbol = "BTCUSDT";
    private static ExecutorService executorService = Executors.newFixedThreadPool(25);
    private static ConcurrentHashMap<Socket, BufferedWriter> clientOutputs = new ConcurrentHashMap<>();
    private static List<Socket> clientSockets = new CopyOnWriteArrayList<>();
    
    // 메시지 전송용 전용 스레드 풀 (최대 5개 클라이언트)
    private static ExecutorService broadcastExecutor = Executors.newFixedThreadPool(5);

    // スポット取引量と先物取引量をクラスレベルで保持して共有
    public static double spotVolumeLastMinute = 0;
    public static double futuresVolumeLastMinute = 0;
    private static long lastResetMinute = -1;

    public static boolean bReconnect = true;
    public static String BinanceSpotUrl = "wss://stream.binance.com:9443/ws/btcusdt@depth20@100ms";
    public static String BinanceFuturesUrl = "wss://fstream.binance.com/ws/btcusdt@depth20@100ms";
    public static String BinanceESpotUrl = "wss://stream.binance.com:9443/ws/ethusdt@depth5@100ms";
    public static String BinanceEFuturesUrl = "wss://fstream.binance.com/ws/ethusdt@depth5@100ms";

    public static String BinanceSpotTradeUrl = "wss://stream.binance.com:9443/ws/btcusdt@aggTrade";
    public static String BinanceFuturesTradeUrl = "wss://fstream.binance.com/ws/btcusdt@aggTrade";

    public static String BybitSpotUrl = "wss://stream.bybit.com/v5/public/spot";
    public static String BybitFuturesUrl = "wss://stream.bybit.com/v5/public/linear";
    public static String OKXSpotUrl = "wss://ws.okx.com:8443/ws/v5/public";
    public static String OKXFuturesUrl = "wss://ws.okx.com:8443/ws/v5/public";
    public static String CoinBaseSpotUrl = "wss://ws-feed.exchange.coinbase.com";

    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }
    
    // 성능 개선된 브로드캐스트 메서드
    private static void broadcastMessage(String typedMessage) {
        // 메시지 유효성 검사 추가
        if (typedMessage == null || typedMessage.trim().isEmpty()) {
            asyncPrint("Warning: Attempting to broadcast empty message, skipping.");
            return;
        }
        
        // JSON 형태 기본 검증
        if (!typedMessage.contains("\"Mtype\"") || !typedMessage.contains("\"message\"")) {
            asyncPrint("Warning: Invalid message format, skipping: " + typedMessage);
            return;
        }
        
        // 현재 연결된 클라이언트 목록 스냅샷
        List<Map.Entry<Socket, BufferedWriter>> clients = new ArrayList<>(clientOutputs.entrySet());
        if (clients.isEmpty()) {
            return;
        }       
        // 병렬로 모든 클라이언트에게 전송
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (Map.Entry<Socket, BufferedWriter> entry : clients) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                Socket socket = entry.getKey();
                BufferedWriter writer = entry.getValue();
                
                try {
                    // 소켓이 여전히 연결되어 있는지 확인
                    if (!socket.isClosed() && socket.isConnected()) {
                        // BufferedWriter를 사용하여 문자열 직접 전송
                        writer.write(typedMessage);
                        writer.newLine();
                        writer.flush();
                    }
                } catch (IOException e) {
                    // 실패한 클라이언트 제거
                    clientOutputs.remove(socket);
                    clientSockets.remove(socket);
                    try {
                        socket.close();
                    } catch (IOException ignored) {}
                }
            }, broadcastExecutor);
            
            futures.add(future);
        }
        
        // 모든 전송이 완료될 때까지 대기 (선택사항)
        // CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public static class WebSocketClient {
        private String url;
        private String msgType;
        private OkHttpClient client;
        private WebSocket webSocket;
        private static final int RECONNECT_DELAY = 5; // 재접속 지연 시간 (초)

        public WebSocketClient(String Url, String msgType) {
            this.client = new OkHttpClient.Builder()
                    .build();
            this.msgType = msgType;
            this.url = Url;
        }

        public void connect() {
            Request request = new Request.Builder()
                    .url(url)
                    .build();
            webSocket = client.newWebSocket(request, new BinanceWebSocketListener());
        }

        public void disconnect() {
            if (webSocket != null) {
                webSocket.close(1000, "Client initiated disconnect:" + msgType);
            }
        }

        private class BinanceWebSocketListener extends WebSocketListener {
            // Moved previous bid/ask size to instance fields so they persist across
            // onMessage calls
            private double previousBidSize = 0;
            private double previousAskSize = 0;

            @Override
            public void onOpen(WebSocket webSocket, okhttp3.Response response) {
                System.out.println("Connected to WebSocket:" + msgType);
                if ("MFBTC20".equals(msgType)) {
                    webSocket.send("{\"op\": \"subscribe\", \"args\": [\"quote:XBTUSD\"]}");
                } else if ("BBTC20".equals(msgType) || "BFBTC20".equals(msgType)) {
                    webSocket.send("{\"op\": \"subscribe\", \"args\": [\"orderbook.1.BTCUSDT\"]}");
                } else if ("OKXBTC20".equals(msgType)) {
                    webSocket.send(
                            "{\"op\": \"subscribe\", \"args\": [{\"channel\": \"bbo-tbt\", \"instId\": \"BTC-USDT\"}]}");
                } else if ("OKXFBTC20".equals(msgType)) {
                    webSocket.send(
                            "{\"op\": \"subscribe\", \"args\": [{\"channel\": \"bbo-tbt\", \"instId\": \"BTC-USDT-SWAP\"}]}");
                } else if ("COBBTC20".equals(msgType)) {
                    webSocket.send(
                            "{\"type\": \"subscribe\", \"channels\": [\"ticker\"], \"product_ids\": [\"BTC-USD\"]}");
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                double trade_orderbook_down20 = 0;
                double trade_orderbook_up20 = 0;
                double trade_price = 0;
                boolean issend = false;
                JSONObject jsonObject = new JSONObject(text);
                // 1分 ボリュームデータ処理ロジック
                if ("SPOT_VOLUME".equals(msgType) && jsonObject.has("e")) {
                    String eventType = jsonObject.getString("e");

                    if ("aggTrade".equals(eventType)) {
                        // 個別取引ボリューム処理
                        double tradeVolume = jsonObject.getDouble("q"); // 取引量
                        spotVolumeLastMinute += tradeVolume; // クラスレベル変数を更新
                    }
                    return;
                } else if ("FUTURES_VOLUME".equals(msgType) && jsonObject.has("e")) {
                    String eventType = jsonObject.getString("e");

                    if ("aggTrade".equals(eventType)) {
                        // 個別取引ボリューム処理
                        double tradeVolume = jsonObject.getDouble("q"); // 取引量
                        futuresVolumeLastMinute += tradeVolume; // クラスレベル変数を更新
                    }
                    return;
                } else if ("MFBTC20".equals(msgType) || "BBTC20".equals(msgType) || "BFBTC20".equals(msgType)) {
                    if (jsonObject.has("data")) {
                        if ("BBTC20".equals(msgType) || "BFBTC20".equals(msgType)) {
                            JSONObject dataObject = jsonObject.getJSONObject("data");
                            JSONArray bidsArray = dataObject.getJSONArray("b");
                            JSONArray asksArray = dataObject.getJSONArray("a");

                            for (int i = 0; i < bidsArray.length(); i++) {
                                trade_orderbook_down20 += bidsArray.getJSONArray(i).getDouble(1);
                            }
                            for (int i = 0; i < asksArray.length(); i++) {
                                trade_orderbook_up20 += asksArray.getJSONArray(i).getDouble(1);
                            }

                        }
                    }
                } else if ("ETH20".equals(msgType) || "FETH20".equals(msgType) || "BTC20".equals(msgType)
                        || "FBTC20".equals(msgType) || "OKXBTC20".equals(msgType) || "OKXFBTC20".equals(msgType)
                        || "COBBTC20".equals(msgType)) {
                    String bids = "bids";
                    String asks = "asks";
                    if ("FBTC20".equals(msgType) || "FETH20".equals(msgType)) {
                        bids = "b";
                        asks = "a";
                    }
                    if (("OKXFBTC20".equals(msgType) || "OKXBTC20".equals(msgType)) && jsonObject.has("data")) {
                        JSONArray dataArray = jsonObject.getJSONArray("data");
                        for (int i = 0; i < dataArray.length(); i++) {
                            JSONObject dataObject = dataArray.getJSONObject(i);

                            // Extract 'asks' and 'bids' arrays
                            JSONArray asksArray = dataObject.getJSONArray("asks");
                            JSONArray bidsArray = dataObject.getJSONArray("bids");

                            // Extract individual ask entries
                            for (int j = 0; j < asksArray.length(); j++) {
                                JSONArray askEntry = asksArray.getJSONArray(j);
                                trade_orderbook_up20 += askEntry.getDouble(1);
                            }

                            // Extract individual bid entries
                            for (int j = 0; j < bidsArray.length(); j++) {
                                JSONArray bidEntry = bidsArray.getJSONArray(j);
                                trade_orderbook_down20 += bidEntry.getDouble(1);
                            }
                        }
                    } else if ("COBBTC20".equals(msgType) && jsonObject.has("best_bid_size")
                            && jsonObject.has("best_ask_size")) {
                        trade_orderbook_down20 = jsonObject.getDouble("best_bid_size");
                        trade_orderbook_up20 = jsonObject.getDouble("best_ask_size");

                        if (trade_orderbook_down20 > 0) {
                            previousBidSize = trade_orderbook_down20;
                        } else {
                            trade_orderbook_down20 = previousBidSize;
                        }
                        if (trade_orderbook_up20 > 0) {
                            previousAskSize = trade_orderbook_up20;
                        } else {
                            trade_orderbook_up20 = previousAskSize;
                        }
                    } else {
                        if (jsonObject.has(bids) && jsonObject.has(asks)) {
                            JSONArray bidsArray = jsonObject.getJSONArray(bids);
                            JSONArray asksArray = jsonObject.getJSONArray(asks);

                            trade_orderbook_down20 = IntStream.range(0, bidsArray.length())
                                    .mapToDouble(i -> bidsArray.getJSONArray(i).getDouble(1))
                                    .sum();

                            trade_orderbook_up20 = IntStream.range(0, asksArray.length())
                                    .mapToDouble(i -> asksArray.getJSONArray(i).getDouble(1))
                                    .sum();

                            trade_price = (asksArray.getJSONArray(0).getDouble(0) > bidsArray.getJSONArray(0)
                                    .getDouble(0)
                                            ? asksArray.getJSONArray(0).getDouble(0)
                                            : bidsArray.getJSONArray(0).getDouble(0));
                        }

                    }
                }

                // Update previous values if current values are non-zero
                previousBidSize = trade_orderbook_down20 > 0 ? trade_orderbook_down20 : previousBidSize;
                previousAskSize = trade_orderbook_up20 > 0 ? trade_orderbook_up20 : previousAskSize;
                // Use previous values if current computed ones are zero
                trade_orderbook_down20 = trade_orderbook_down20 > 0 ? trade_orderbook_down20 : previousBidSize;
                trade_orderbook_up20 = trade_orderbook_up20 > 0 ? trade_orderbook_up20 : previousAskSize;

                issend = (trade_orderbook_down20 != 0 && trade_orderbook_up20 != 0);

                if (issend) {
                    DecimalFormat df = new DecimalFormat("#.##########");
                    String formattedDown20 = df.format(trade_orderbook_down20);
                    String formattedUp20 = df.format(trade_orderbook_up20);
                    String trade_prices = df.format(trade_price);

                    // クラスレベル変数から最新のボリューム情報を取得
                    //String spotVolume = df.format(spotVolumeLastMinute);
                    //String futuresVolume = df.format(futuresVolumeLastMinute);

                    String message = formattedDown20 + "-" + formattedUp20 + "-" + trade_prices;// + "-" + spotVolume + "-" + futuresVolume;

                    String typedMessage = "{\"Mtype\": \"" + msgType + "\", \"message\": \"" + message + "\"}";

                    // 성능 개선된 브로드캐스트 호출
                    broadcastMessage(typedMessage);
                    //spotVolumeLastMinute = 0;
                    //futuresVolumeLastMinute = 0;
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                onMessage(webSocket, bytes.utf8());
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                System.out.println("Closing:" + msgType + " WebSocket: " + reason);
                reconnect(webSocket);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, okhttp3.Response response) {
                t.printStackTrace();
                reconnect(webSocket);
            }

            private void reconnect(WebSocket webSocket) {
                if (bReconnect) {
                    System.out.println("Reconnecting in " + RECONNECT_DELAY + " seconds...");
                    webSocket.close(1000, "Closing due to failure:" + msgType);
                    try {
                        TimeUnit.SECONDS.sleep(RECONNECT_DELAY);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    connect();
                }
            }
        }
    }

    public static void main(String[] args) {
        int port = 22222;

        System.out.println("RUN GetOrderBookServer : " + port);

        List<WebSocketClient> webSocketClients = new ArrayList<>();

        webSocketClients.add(new WebSocketClient(BinanceSpotUrl, "BTC20"));
        webSocketClients.add(new WebSocketClient(BinanceFuturesUrl, "FBTC20"));
        //webSocketClients.add(new WebSocketClient(BinanceSpotTradeUrl, "SPOT_VOLUME"));
        //webSocketClients.add(new WebSocketClient(BinanceFuturesTradeUrl, "FUTURES_VOLUME"));
        // webSocketClients.add(new WebSocketClient(BinanceESpotUrl, "ETH20"));
        // webSocketClients.add(new WebSocketClient(BinanceEFuturesUrl, "FETH20"));
        // webSocketClients.add(new WebSocketClient(BybitSpotUrl, "BBTC20"));
        // webSocketClients.add(new WebSocketClient(BybitFuturesUrl, "BFBTC20"));
        // webSocketClients.add(new WebSocketClient(OKXSpotUrl, "OKXBTC20"));
        // webSocketClients.add(new WebSocketClient(OKXFuturesUrl, "OKXFBTC20"));
        // webSocketClients.add(new WebSocketClient(CoinBaseSpotUrl, "COBBTC20"));
        for (WebSocketClient client : webSocketClients) {
            client.connect();
        }

        ScheduledExecutorService schedulerreconnect = Executors.newScheduledThreadPool(1);
        schedulerreconnect.scheduleAtFixedRate(() -> reconnectAllClients(webSocketClients), 1, 1, TimeUnit.HOURS);
        
        ServerSocket serverSocket;
        try {
            serverSocket = new ServerSocket(port);
            while (true) {
                Socket clientSocket = serverSocket.accept();
                System.out.println("Client connected: " + clientSocket.getInetAddress() + ":" + clientSocket.getPort());
                clientSockets.add(clientSocket);
                executorService.submit(() -> handleClientConnection(clientSocket));
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    private static void handleClientConnection(Socket clientSocket) {
        BufferedWriter out = null;
        try {
            OutputStream outStream = clientSocket.getOutputStream();
            out = new BufferedWriter(new OutputStreamWriter(outStream), 8192);

            // TCP 최적화 설정
            clientSocket.setTcpNoDelay(true);
            clientSocket.setKeepAlive(true);
            clientSocket.setSendBufferSize(65536);

            clientOutputs.put(clientSocket, out); // ConcurrentHashMap에 추가

            // 클라이언트가 연결을 끊을 때까지 대기 (클라이언트로부터의 입력은 무시)
            while (!clientSocket.isClosed()) {
                try {
                    // 소켓이 여전히 연결되어 있는지 확인
                    if (clientSocket.getInputStream().read() == -1) {
                        break; // 스트림 끝에 도달하면 연결 종료
                    }
                } catch (SocketTimeoutException e) {
                    // 타임아웃 발생시 계속 대기
                    continue;
                } catch (IOException e) {
                    break; // 다른 IO 에러 발생시 연결 종료
                }
            }

        } catch (SocketException e) {
            System.out.println("Client disconnected: " + clientSocket.getInetAddress());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (clientSocket != null) {
                clientOutputs.remove(clientSocket);
            }
            closeClientSocket(clientSocket);
        }
    }

    private static void closeClientSocket(Socket clientSocket) {
        try {
            System.out.println(clientSocket.getInetAddress() + " Close Client");
            clientSocket.close();
            clientSockets.remove(clientSocket);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void reconnectAllClients(List<WebSocketClient> webSocketClients) {
        bReconnect = false;
        for (WebSocketClient client : webSocketClients) {
            client.disconnect();
        }

        webSocketClients.clear();

        webSocketClients.add(new WebSocketClient(BinanceSpotUrl, "BTC20"));
        webSocketClients.add(new WebSocketClient(BinanceFuturesUrl, "FBTC20"));
       // webSocketClients.add(new WebSocketClient(BinanceSpotTradeUrl, "SPOT_VOLUME"));
       // webSocketClients.add(new WebSocketClient(BinanceFuturesTradeUrl, "FUTURES_VOLUME"));

        // webSocketClients.add(new WebSocketClient(BinanceESpotUrl, "ETH20"));
        // webSocketClients.add(new WebSocketClient(BinanceEFuturesUrl, "FETH20"));
        // webSocketClients.add(new WebSocketClient(BybitSpotUrl, "BBTC20"));
        // webSocketClients.add(new WebSocketClient(BybitFuturesUrl, "BFBTC20"));
        // webSocketClients.add(new WebSocketClient(OKXSpotUrl, "OKXBTC20"));
        // webSocketClients.add(new WebSocketClient(OKXFuturesUrl, "OKXFBTC20"));
        // webSocketClients.add(new WebSocketClient(CoinBaseSpotUrl, "COBBTC20"));
        for (WebSocketClient client : webSocketClients) {
            client.connect();
        }
        bReconnect = true;
    }
    
    // 종료 시 리소스 정리를 위한 shutdown hook 추가
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            broadcastExecutor.shutdown();
            executorService.shutdown();
            PrintService.shutdown();
            try {
                if (!broadcastExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    broadcastExecutor.shutdownNow();
                }
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
                if (!PrintService.awaitTermination(5, TimeUnit.SECONDS)) {
                    PrintService.shutdownNow();
                }
            } catch (InterruptedException e) {
                broadcastExecutor.shutdownNow();
                executorService.shutdownNow();
                PrintService.shutdownNow();
            }
        }));
    }
}
