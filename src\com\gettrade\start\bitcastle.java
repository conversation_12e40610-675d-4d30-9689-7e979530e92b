package com.gettrade.start;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class bitcastle {

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_bcgame();

        public void TradeStart(int nflag, String value);
    }

    public static int BUY_UP = 700001;
    public static int SELL_DOWN = 700002;
    public static int SET_AMOUNT = 700003;
    public static int WINDOWS_TOP = 300008;

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    public static double volume = 100;
    public static double volumeBTC = 0;
    public static double orderbook_S1 = 0.001;
    public static double orderbook_E1 = 1.5;
    public static double orderbook_S2 = 1.5;
    public static double orderbook_E2 = 1.5;
    public static double volumeBTC_Check = 15;

    public static double trade_volume_max = 15;
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;
    public static int BollCheck = 0;
    public static int checkboll = 0;
    public static int TradeFlg = 3;
    public static int INIT_WAIT = 0;
    public static int SLEEP = 1000;
    public static int TRACE = 0;
    public static String AMOUNT = "1";
    public static String AMOUNT_ATTK = "1";
    public static int ORDERBOOK_CNT = 0;
    public static int ORDERBOOK_STOP = 0;

    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false);
    private static AtomicBoolean first = new AtomicBoolean(false);

    public static String symbol = "BTCUSDT";
    public static String signal1 = "";
    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    static class MessageHandler1 implements Runnable {

        @Override
        public void run() {
            long lastGpuCleanupTime = System.currentTimeMillis();
            getGpuMemoryUsagePercentage();

            while (true) {
                if (ORDERBOOK_CNT <= ORDERBOOK_STOP && first.get() && volume <= trade_volume_max
                        && volumeBTC <= volumeBTC_Check) {
                    System.out.println("=== predictDirection ===");
                    updatePredictions();
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastGpuCleanupTime >= 4 * 60 * 60 * 1000) {
                        getGpuMemoryUsagePercentage();
                        System.out.println("Scheduled GPU memory cleanup executed");
                        lastGpuCleanupTime = currentTime;
                    }
                } else {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    signal1 = "PENDING";
                }
            }
        }

        public static void getGpuMemoryUsagePercentage() {
            // Execute ollama stop command to free up GPU memory
            try {
                System.out.println("Executing: ollama stop gemma3:12bk");
                ProcessBuilder stopCmd = new ProcessBuilder("ollama", "stop", "gemma3:12bk");
                stopCmd.redirectErrorStream(true);
                Process stopProcess = stopCmd.start();

                // Read the command output
                ByteArrayOutputStream stopOutput = new ByteArrayOutputStream();
                try (InputStream is = stopProcess.getInputStream()) {
                    int c;
                    while ((c = is.read()) != -1) {
                        stopOutput.write(c);
                    }
                }

                if (!stopProcess.waitFor(10, TimeUnit.SECONDS)) {
                    stopProcess.destroyForcibly();
                    System.err.println("Timeout waiting for ollama stop command");
                } else {
                    String cmdOutput = stopOutput.toString(java.nio.charset.StandardCharsets.UTF_8).trim();
                    System.out.println("ollama stop command result: " + cmdOutput);
                }
            } catch (Exception ex) {
                System.err.println("Error stopping ollama model: " + ex.getMessage());
                ex.printStackTrace();
            }
        }

        public static void updatePredictions() {
            String[] predictionResults = runPredictionTwice();

            signal1 = predictionResults[0];

            // Count the number of BUY and SELL signals
            int buyCount = 0;
            int sellCount = 0;

            for (String signal : predictionResults) {
                if (signal.equals("BUY")) {
                    buyCount++;
                } else if (signal.equals("SELL")) {
                    sellCount++;
                }
            }

            // Set signal1 and TradeFlg based on the counts
            if (sellCount >= 2) {
                signal1 = "SELL";
                TradeFlg = 0;
            } else if (buyCount >= 2) {
                signal1 = "BUY";
                TradeFlg = 0;
            } else {
                signal1 = "PENDING";
                TradeFlg = 3;
            }
        }

        public static String predictDirection(int modelNum) {
            try {
                ProcessBuilder pb = new ProcessBuilder("python", "auto_trader_real_1m.py",
                        String.valueOf(modelNum));
                pb.directory(new java.io.File("C:/Trade/pytrade/"));
                pb.redirectErrorStream(true); // Merge error stream with output
                Process p = pb.start();

                // Drain the process output concurrently to prevent blocking.
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                Thread outputReader = new Thread(() -> {
                    try (InputStream is = p.getInputStream()) {
                        int c;
                        while ((c = is.read()) != -1) {
                            baos.write(c);
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
                outputReader.start();

                if (!p.waitFor(3, java.util.concurrent.TimeUnit.MINUTES)) {
                    p.destroyForcibly();
                    System.err.println("Timeout waiting for python script");
                    return "ERROR";
                }
                outputReader.join(); // Ensure all output is read

                int exitCode = p.exitValue();
                System.out.println("Python script exit code: " + exitCode);
                if (exitCode == 1)
                    return "ERROR";

                String output = baos.toString(java.nio.charset.StandardCharsets.UTF_8);
                String[] outputLines = output.split("\\r?\\n");
                String lastLine = "";
                for (int i = outputLines.length - 1; i >= 0; i--) {
                    if (!outputLines[i].trim().isEmpty()) {
                        lastLine = outputLines[i].trim();
                        break;
                    }
                }
                if (lastLine.isEmpty()) {
                    System.err.println("No output from python script.");
                    return "ERROR";
                }
                if (lastLine.contains("result: 0"))
                    return "HOLD";
                else if (lastLine.contains("result: 2"))
                    return "BUY";
                else if (lastLine.contains("result: 1"))
                    return "SELL";
                else if (lastLine.contains("result: 9"))
                    return "PENDING";
                else
                    return "ERROR";
            } catch (Exception e) {
                e.printStackTrace();
                return "ERROR";
            }
        }

        public static String trendPredictor(String parm) {
            try {
                ProcessBuilder pb = new ProcessBuilder("python", "bitcoin_trend_predictor.py",
                        "--interval", parm, "one");
                pb.directory(new java.io.File("C:/Trade/pytrade/"));
                pb.redirectErrorStream(true); // Merge error stream with output
                Process p = pb.start();

                // Drain the process output concurrently to prevent blocking.
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                Thread outputReader = new Thread(() -> {
                    try (InputStream is = p.getInputStream()) {
                        int c;
                        while ((c = is.read()) != -1) {
                            baos.write(c);
                        }
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                });
                outputReader.start();

                if (!p.waitFor(3, java.util.concurrent.TimeUnit.MINUTES)) {
                    p.destroyForcibly();
                    System.err.println("Timeout waiting for python script");
                    return "ERROR";
                }
                outputReader.join(); // Ensure all output is read

                int exitCode = p.exitValue();
                System.out.println("Python script exit code: " + exitCode);
                if (exitCode == 1)
                    return "ERROR";

                String output = baos.toString(java.nio.charset.StandardCharsets.UTF_8);
                String[] outputLines = output.split("\\r?\\n");
                String lastLine = "";
                for (int i = outputLines.length - 1; i >= 0; i--) {
                    if (!outputLines[i].trim().isEmpty()) {
                        lastLine = outputLines[i].trim();
                        break;
                    }
                }
                if (lastLine.isEmpty()) {
                    System.err.println("No output from python script.");
                    return "ERROR";
                }
                if (lastLine.contains("result: 0"))
                    return "HOLD";
                else if (lastLine.contains("result: 2"))
                    return "BUY";
                else if (lastLine.contains("result: 1"))
                    return "SELL";
                else if (lastLine.contains("result: 9"))
                    return "PENDING";
                else
                    return "ERROR";
            } catch (Exception e) {
                e.printStackTrace();
                return "ERROR";
            }
        }

        public static String[] runPredictionTwice() {
            ExecutorService executor = Executors.newFixedThreadPool(2);
            Future<String> future1 = null;
            Future<String> future2 = null;
            String[] results = new String[2];

            try {
                // Start two identical prediction tasks
                Callable<String> task1 = () -> predictDirection(3);
                Callable<String> task2 = () -> trendPredictor("5m");

                future1 = executor.submit(task1);
                Thread.sleep(SLEEP);
                future2 = executor.submit(task2);

                results[0] = future1.get(3, TimeUnit.MINUTES);
                results[1] = future2.get(3, TimeUnit.MINUTES);
                System.out
                        .println("Both prediction threads completed. Results: " + results[0] + ", " + results[1] + ", "
                                + results[2]);
            } catch (Exception e) {
                System.err.println("Error in parallel predictions: " + e.getMessage());
                e.printStackTrace();

                // Fill any null results with "ERROR"
                for (int i = 0; i < results.length; i++) {
                    if (results[i] == null) {
                        results[i] = "ERROR";
                    }
                }

            } finally {
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            return results;
        }
    }

    static class MessageHandler implements Runnable {
        private boolean bfirstBTC20 = false;
        private boolean bfirstFBTC20 = false;

        private double tradeOrderbookDownF20 = 99;
        private double tradeOrderbookUpF20 = 99;
        private double tradeOrderbookDown20 = 99;
        private double tradeOrderbookUp20 = 99;

        @Override
        public void run() {

            while (true) {
                try (Socket socket = new Socket("192.168.11.14", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    while (true) {
                        String message = in.readLine();
                        if (message != null) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            if ((currentSecond >= 59 && currentSecond <= 59) || !first.get()) {
                                if (initTrade.compareAndSet(false, true)) {
                                    messageProcessingService.submit(() -> {
                                        InitMain();
                                        try {
                                            Thread.sleep(1100);
                                        } catch (InterruptedException e) {
                                            e.printStackTrace();
                                        }
                                        first.set(true);
                                        resetTradeOrderbook();
                                        resetFlags();
                                        initTrade.set(false);
                                    });
                                }
                            } else if (first.get()) {
                                if (initTrade.compareAndSet(false, true)) {
                                    //messageProcessingService.submit(() -> {
                                    processMessage(message);
                                    initTrade.set(false);
                                    //});
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    resetTradeOrderbook();
                    resetFlags();
                    initTrade.set(false);
                    try {
                        Thread.sleep(5000); // Wait 5 seconds before attempting to reconnect
                    } catch (InterruptedException ie) {
                        ie.printStackTrace();
                    }
                }
            }
        }

        private void processMessage(String message) {
            if (TradeFlg > 2) {
                resetTradeOrderbook();
                resetFlags();
                return;
            }
            if (message != null) {
                // Process the message based on its type
                JsonObject jsonObject = JsonParser.parseString(message).getAsJsonObject();
                if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                    String mtype = jsonObject.get("Mtype").getAsString();
                    if ("BTC20".equals(mtype)) {
                        bfirstBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        String[] orderbook20MSg = msg.split("-");
                        tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                        tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                    } else if ("FBTC20".equals(mtype)) {
                        bfirstFBTC20 = true;
                        String msg = jsonObject.get("message").getAsString();
                        String[] orderbook20MSg = msg.split("-");
                        tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                        tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                    }

                    if (ORDERBOOK_CNT == ORDERBOOK_STOP + 1) {
                        System.out.println("Trade Stop ORDERBOOK_CNT:" + ORDERBOOK_CNT);
                    }
                    /*
                    if (BollCheck == 1 && checkboll == 1) {
                    asyncPrint("NOT TRADE");
                    TradeFlg = 3;
                    return;
                    }
                    */
                    if ((ORDERBOOK_CNT <= ORDERBOOK_STOP || TradeFlg < 3) && volume <= trade_volume_max
                            && volumeBTC <= volumeBTC_Check
                            && bfirstBTC20
                            && bfirstFBTC20
                            && tradeOrderbookUpF20 != 99 && tradeOrderbookDownF20 != 99 && tradeOrderbookUp20 != 99
                            && tradeOrderbookDown20 != 99) {

                        if ((TradeFlg == 2 || (TradeFlg == 0 && signal1.equals("BUY")))
                                && ((orderbook_S1 < tradeOrderbookUp20 && tradeOrderbookUp20 < orderbook_E1)
                                        && (orderbook_S2 < tradeOrderbookDown20
                                                && tradeOrderbookDown20 < orderbook_E2)
                                        && (orderbook_S1 < tradeOrderbookUpF20 && tradeOrderbookUpF20 < orderbook_E1)
                                        && (orderbook_S2 < tradeOrderbookDownF20
                                                && tradeOrderbookDownF20 < orderbook_E2))
                                && INIT_WAIT > 0) {

                            TradeFlg = 1;
                            Trade.INSTANCE.TradeStart(BUY_UP, "");
                            System.out.println("BINAINCE Trade Buy volume:" + volume + " volumeBTC:"
                                    + volumeBTC + " up20:" + tradeOrderbookUp20 + " down20:"
                                    + tradeOrderbookDown20 + " upF20:" + tradeOrderbookUpF20 + " downF20:"
                                    + tradeOrderbookDownF20);
                            resetTradeOrderbook();
                            resetFlags();
                            if (TradeFlg == 2) {
                                TradeFlg = 3;
                            }
                            ORDERBOOK_CNT++;
                        } else if ((TradeFlg == 1 || (TradeFlg == 0 && signal1.equals("SELL")))
                                && ((orderbook_S1 < tradeOrderbookDown20 && tradeOrderbookDown20 < orderbook_E1)
                                        && (orderbook_S2 < tradeOrderbookUp20
                                                && tradeOrderbookUp20 < orderbook_E2)
                                        && (orderbook_S1 < tradeOrderbookDownF20
                                                && tradeOrderbookDownF20 < orderbook_E1)
                                        && (orderbook_S2 < tradeOrderbookUpF20
                                                && tradeOrderbookUpF20 < orderbook_E2))
                                && INIT_WAIT > 0) {

                            TradeFlg = 2;
                            Trade.INSTANCE.TradeStart(SELL_DOWN, "");
                            System.out.println("BINAINCE Trade Sell volume:" + volume + " volumeBTC:"
                                    + volumeBTC + " up20:" + tradeOrderbookUp20 + " down20:"
                                    + tradeOrderbookDown20 + " upF20:" + tradeOrderbookUpF20 + " downF20:"
                                    + tradeOrderbookDownF20);
                            resetTradeOrderbook();
                            resetFlags();
                            if (TradeFlg == 1) {
                                TradeFlg = 3;
                            }
                            ORDERBOOK_CNT++;
                        }

                        if (TRACE == 1) {
                            asyncPrint("TRACE volume:" + volume + " volumeBTC:" + volumeBTC + " up20:"
                                    + tradeOrderbookUp20 + " dn20:" + tradeOrderbookDown20 + " upF20:"
                                    + tradeOrderbookUpF20 + " dnF20:" + tradeOrderbookDownF20);
                        }
                    }
                }
            }
        }

        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = 99;
            tradeOrderbookUpF20 = 99;
            tradeOrderbookDown20 = 99;
            tradeOrderbookUp20 = 99;
        }

        private void resetFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

    }

    public static void InitMain() {

        try {
            if (REFRESH_CNT > 10) {

                Trade.INSTANCE.TradeStart(REFRESH, "");
                REFRESH_CNT = 0;
                Thread.sleep(6000);
            }
            REFRESH_CNT++;
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        try {
            volume = 0;
            List<Candlestick> candlestickList = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20,
                    null, null);

            Candlestick candlestick = null;
            List<Double> priceList = new ArrayList<>();
            for (int i = 0; i < candlestickList.size(); i++) {
                candlestick = candlestickList.get(i);
                if (i >= 9 && i < candlestickList.size() - 1)
                    volume += Double.parseDouble(candlestick.getVolume());

                priceList.add(Double.parseDouble(candlestick.getClose()));
            }
            volume = volume / 10;
            volume = roundToTwoDecimalPlaces(volume);

            BollingerBands boll = new BollingerBands(priceList, 20, 2.0);
            BollDown = boll.getdownBollingerBands();
            BollUp = boll.getUpBollingerBands();

            if (Double.parseDouble(candlestick.getHigh()) > BollUp
                    || Double.parseDouble(candlestick.getLow()) < BollDown)
                BollCheck = 1;
            else
                BollCheck = 0;

            volumeBTC = 0;
            candlestickList = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20, null, null);

            for (int i = 0; i < candlestickList.size(); i++) {
                candlestick = candlestickList.get(i);
                if (i >= 9 && i < candlestickList.size() - 1)
                    volumeBTC += Double.parseDouble(candlestick.getVolume());
            }

            volumeBTC = volumeBTC / 10;
            volumeBTC = roundToTwoDecimalPlaces(volumeBTC);

            String filePath = "";
            filePath = "c:/Trade/TradeBitcastle.properties";

            try (InputStream input = new FileInputStream(filePath)) {
                Properties prop = new Properties();
                prop.load(input);

                orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1", "0.001"));
                orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1", "1"));
                orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2", "4"));
                orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2", "10"));

                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
                trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
                checkboll = Integer.parseInt(prop.getProperty("checkboll", "0"));
                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
                AMOUNT = prop.getProperty("AMOUNT", "1");
                AMOUNT_ATTK = prop.getProperty("AMOUNT_ATTK", "1");
                SLEEP = Integer.parseInt(prop.getProperty("SLEEP", "1000"));
                TRACE = Integer.parseInt(prop.getProperty("TRACE", "0"));
                ORDERBOOK_STOP = Integer.parseInt(prop.getProperty("ORDERBOOK_STOP", "5"));
            }

            asyncPrint("orderbook_S1:" + orderbook_S1);
            asyncPrint("orderbook_E1:" + orderbook_E1);
            asyncPrint("orderbook_S2:" + orderbook_S2);
            asyncPrint("orderbook_E2:" + orderbook_E2);

            asyncPrint("volumeBTC_Check:" + volumeBTC_Check);
            asyncPrint("trade_volume_max:" + trade_volume_max);
            asyncPrint("BollUp:" + BollUp);
            asyncPrint("BollDown:" + BollDown);
            asyncPrint("volume:" + volume);
            asyncPrint("volumeBTC:" + volumeBTC);
            asyncPrint("checkboll:" + checkboll);
            asyncPrint("INIT_WAIT:" + INIT_WAIT);
            asyncPrint("AMOUNT:" + AMOUNT);
            asyncPrint("AMOUNT_ATTK:" + AMOUNT_ATTK);
            asyncPrint("SLEEP:" + SLEEP);
            asyncPrint("TRACE:" + TRACE);
            asyncPrint("ORDERBOOK_STOP:" + ORDERBOOK_STOP);

        } catch (IOException e) {
            asyncPrint("Failed to get candlestick data: " + e.getMessage());
            volume = 100;
            return;
        }
        if (volumeBTC > 8 && volume > 80) {
            Trade.INSTANCE.TradeStart(SET_AMOUNT, AMOUNT);
        } else if (volumeBTC <= 5 && volume <= 30) {
            // Convert AMOUNT_ATTK to double, multiply by 2, and convert back to string
            double amount = Integer.parseInt(AMOUNT_ATTK) * 2;
            String doubledAmount = String.valueOf(amount);
            Trade.INSTANCE.TradeStart(SET_AMOUNT, doubledAmount);
        } else {
            Trade.INSTANCE.TradeStart(SET_AMOUNT, AMOUNT_ATTK);
        }
        ORDERBOOK_CNT = 0;
    }

    public static void main(String[] args) throws InterruptedException, IOException {

        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();

        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        Trade.INSTANCE.TradeBitqDlg_bcgame();

        Trade.INSTANCE.TradeStart(WINDOWS_TOP, "");
        Trade.INSTANCE.TradeStart(TIME_RESET, "");

        try {
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        //Trade.INSTANCE.TradeStart(1);

        Thread thread = new Thread(new MessageHandler());
        thread.start();

        Thread thread1 = new Thread(new MessageHandler1());
        thread1.start();

        while (true) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df = new DecimalFormat("#.####");
        return Double.parseDouble(df.format(value));
    }

}