package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class CapitalcoreF {

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_expert();
        public int GetUpDownSecRcv();
        public int SetUpDownSecRcv();
        public void TradeStart(int nflag);
    }

    public static int BUY_UP = 500111;
    public static int SELL_DOWN = 500112;
    public static int PROFIT = 500113;
    public static int CLOSE = 500114;
    public static int CHECK = 500115;
    public static int WINDOWS_TOP = 300008;

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static int REFRESH_CNT = 0;
    public static double volume = 0; // 전체 퓨처 거래량 (과거 데이터 기반)
    public static double volumeBTC = 0; // 전체 스팟 거래량 (과거 데이터 기반)
    public static double orderbook_S1 = 0.001;
    public static double orderbook_E1 = 1.5;
    public static double orderbook_S2 = 1.5;
    public static double orderbook_E2 = 1.5;
    public static double volumeBTC_Check = 15; // 스팟 거래량 임계값 (설정값)

    public static double trade_volume_max = 15; // 퓨처 거래량 임계값 (설정값)
    public static double BollUp = 0.0;
    public static double BollDown = 0.0;
    public static int BollCheck = 0; // 볼린저 밴드 이탈 여부 (1: 이탈)
    public static int checkboll = 0; // 볼린저 밴드 체크 활성화 여부 (설정값)
    public static int TradeFlg = 0; // 거래 상태 플래그 (0: 대기, 1: 매수 시도, 2: 매도 시도, 3: 거래 중지)
    public static int INIT_WAIT = 0; // 초기 대기 시간 관련 플래그 (설정값)
    public static int TRACE = 0; // 디버깅 로그 레벨 (설정값)


    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;
    private static AtomicBoolean initTrade = new AtomicBoolean(false); // 초기화 진행 중 플래그
    private static AtomicBoolean first = new AtomicBoolean(false); // 첫 메시지 수신 여부 플래그

    public static String symbol = "BTCUSDT";
    private static final ExecutorService messageProcessingService = Executors.newCachedThreadPool();
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // --- MessageHandler (Main Trading Logic) ---
    static class MessageHandler implements Runnable {
        // 오더북 및 가격 데이터
        private volatile double tradeOrderbookDownF20 = 99;
        private volatile double tradeOrderbookUpF20 = 99;
        private volatile double tradeOrderbookDown20 = 99;
        private volatile double tradeOrderbookUp20 = 99;
        private volatile double tradePriceF = -999999; // 선물 현재가
        private volatile double tradePrice = -999999; // 스팟 현재가
        private volatile double oldtradePriceF = 0;
        private volatile double oldtradePrice = 0;

        // 데이터 수신 플래그
        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;
        
        // 거래량 이력 리스트를 가격 이력 리스트로 변경
        private final List<Double> spotPriceHistory = new ArrayList<>();
        private final List<Double> futuresPriceHistory = new ArrayList<>();
        private static final int MAX_PRICE_HISTORY = 5; // 최대 20개의 이력을 보관

        @Override
        public void run() {
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("Data receiving socket connection successful.");
                    resetDataFlags(); // 연결 시 플래그 초기화

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty()) {
                            long totalMilliseconds = System.currentTimeMillis();
                            long currentSecond = (totalMilliseconds / 1000) % 60;

                            // 매 분 59초 또는 첫 실행 시 초기화 작업 수행
                            if ((currentSecond == 59 || !first.get())) {
                                if (initTrade.compareAndSet(false, true)) {
                                    messageProcessingService.submit(() -> {
                                        try {
                                            InitMain(); // 설정값 및 볼린저 밴드 등 업데이트
                                            if(TradeFlg == 4) {
                                                TradeFlg = 0; // 거래 중지 상태 초기화
                                            }
                                            resetTradeOrderbook(); // 오더북 데이터 초기화
                                            resetDataFlags(); // 데이터 수신 플래그 초기화 -> 연결 시 한 번만 수행하도록 변경
                                            Thread.sleep(1000);
                                        } catch (Exception e) {
                                            asyncPrint("Error during initialization: " + e.getMessage());
                                            e.printStackTrace();
                                        } finally {
                                            first.set(true); // 첫 실행 완료 플래그 설정
                                            initTrade.set(false);
                                        }
                                    });
                                }
                            } else if (first.get()) { // 첫 실행 이후
                                if (initTrade.compareAndSet(false, true)) { // 다른 작업 중이 아닐 때만 메시지 처리
                                    try {
                                        processMessage(message); // 핵심 로직: 메시지 처리 및 거래 결정
                                    } catch (Exception e) {
                                        asyncPrint("Error while processing message: " + e.getMessage());
                                        e.printStackTrace();
                                    } finally {
                                        initTrade.set(false);
                                    }
                                }
                            }
                        } 
                    }
                } catch (IOException e) {
                    asyncPrint("Socket connection error or data receiving error: " + e.getMessage());
                    resetTradeOrderbook();
                    resetDataFlags();
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    initTrade.set(false); // 작업 중 플래그 해제
                    first.set(false); // 첫 실행 플래그 리셋
                    try {
                        Thread.sleep(5000); // 5초 후 재연결 시도
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        asyncPrint("Interrupt occurred while waiting for reconnection.");
                    }
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    ex.printStackTrace();
                    initTrade.set(false);
                    try {
                        Thread.sleep(1000); // 잠시 대기
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        /**
         * 수신된 메시지를 처리하고 거래 조건을 확인하여 거래를 실행합니다.
         */
        private void processMessage(String message) {
            if (message == null || message.isEmpty()) {
                asyncPrint("Empty message received, skipping processing.");
                return;
            }

            if (checkboll == 1 && BollCheck == 1) {
                if (TRACE > 0)
                    asyncPrint("Bollinger Bands deviation, trading not allowed.");
                    TradeFlg = 4;
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    return;
                }

            if ((volume > trade_volume_max || volumeBTC > volumeBTC_Check)) {
                if (TRACE > 0)
                    asyncPrint("Total volume exceeded (F:" + volume + ", S:" + volumeBTC
                        + "), trading not allowed.");
                    TradeFlg = 4;
                    spotPriceHistory.clear();
                    futuresPriceHistory.clear();
                    return;
                }

            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);

                while (reader.hasNext()) {

                    JsonObject jsonObject = null;
                    try {
                        jsonObject = JsonParser.parseReader(reader).getAsJsonObject();
                    } catch (Exception e) {
                        if (TRACE > 0) {
                            asyncPrint("JSON parsing error in stream: " + e.getMessage());
                        }
                        continue;
                    }

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();

                        try {
                            if ("BTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-", 3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePrice = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstBTC20 = true;
                                } else {
                                    asyncPrint("Spot data format error: " + msg);
                                }
                            } else if ("FBTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-", 3);
                                if (orderbook20MSg.length >= 3) {
                                    tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                                    tradePriceF = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstFBTC20 = true;
                                } else {
                                    asyncPrint("Futures data format error: " + msg);
                                }
                            }
                        } catch (NumberFormatException e) {
                            asyncPrint("Number format error while parsing data: " + msg + " / Error: " + e.getMessage());
                            return;
                        } catch (Exception e) {
                            asyncPrint("Exception while processing data: " + msg + " / Error: " + e.getMessage());
                            e.printStackTrace();
                            return;
                        }

                        if (bfirstBTC20 && bfirstFBTC20) {
                            if (tradeOrderbookUp20 == 99 || tradeOrderbookDown20 == 99 || tradeOrderbookUpF20 == 99
                                    || tradeOrderbookDownF20 == 99 || tradePrice == -999999 || tradePriceF == -999999) {
                                return;
                            }

                            boolean potentialBuy = false;
                            boolean potentialSell = false;
                            double priceChangeThreshold = 9;
                            if (spotPriceHistory.size() >= MAX_PRICE_HISTORY && futuresPriceHistory.size() >= MAX_PRICE_HISTORY && oldtradePriceF != -999999 && oldtradePrice != -999999) {
                                double spotChange = tradePrice - oldtradePrice;
                                double futuresChange = tradePriceF - oldtradePriceF;
                                if (spotChange > priceChangeThreshold || futuresChange > priceChangeThreshold) {
                                    potentialBuy = true;
                                } else if (spotChange < -priceChangeThreshold || futuresChange < -priceChangeThreshold) {
                                    potentialSell = true;
                                }
                            }

                            if (potentialBuy && !potentialSell) {
                                executeTradeOnOrderbookEvent(true, Math.min(tradeOrderbookUp20, tradeOrderbookUpF20));
                            } else if (potentialSell && !potentialBuy) {
                                executeTradeOnOrderbookEvent(false, Math.min(tradeOrderbookDown20, tradeOrderbookDownF20));
                            }

                            updatePriceHistory(tradePrice, tradePriceF);
                            oldtradePriceF = tradePriceF;
                            oldtradePrice = tradePrice;
                        }
                    }
                }
            } catch (IOException e) {
                asyncPrint("IOException during JSON stream processing: " + e.getMessage());
            }
        }

        /**
         * 현재 시장 상황 요약 문자열 반환 (로깅용)
         */
        private String getMarketSummary() {
            return " | OB(S U/D): " + formatDecimal(tradeOrderbookUp20) + "/" + formatDecimal(tradeOrderbookDown20) +
                    " | OB(F U/D): " + formatDecimal(tradeOrderbookUpF20) + "/" + formatDecimal(tradeOrderbookDownF20) +
                    " | Price(S/F): " + formatDecimal(tradePrice) + "/" + formatDecimal(tradePriceF) +
                    " | PriceOld(S/F): " + formatDecimal(oldtradePrice) + "/" + formatDecimal(oldtradePriceF);
        }

        /** DecimalFormat 캐싱 */
        private static final DecimalFormat df = new DecimalFormat("#.###");

        private String formatDecimal(double value) {
            synchronized (df) { // DecimalFormat은 스레드 안전하지 않으므로 동기화
                return df.format(value);
            }
        }

        /** 오더북 데이터 초기화 */
        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = 99;
            tradeOrderbookUpF20 = 99;
            tradeOrderbookDown20 = 99;
            tradeOrderbookUp20 = 99;
            tradePrice = -999999;
            tradePriceF = -999999;
            oldtradePrice=-999999;
            oldtradePriceF=-999999;
            
        }

        /** 데이터 수신 플래그 초기화 */
        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }

        // 오더북 이벤트 감지 및 거래 실행 메서드
        private void executeTradeOnOrderbookEvent(boolean isBuySignal, double orderBookSize) {
                    	                    		            
            if (TradeFlg == 0 && INIT_WAIT > 0) {
            	boolean priceConditionMet = isPriceFluctuationSignificant(isBuySignal);
            	
	            // 거래 실행
	            if (isBuySignal) {	
	                if (priceConditionMet) { 
	                	TradeFlg = 3;
		                Trade.INSTANCE.TradeStart(BUY_UP);

		                asyncPrint("### Orderbook-based BUY executed ### Orderbook size: " +
		                    formatDecimal(orderBookSize) + getMarketSummary());
	                }
	            } else {
	                if (priceConditionMet) { 
	                	TradeFlg = 3;
		                Trade.INSTANCE.TradeStart(SELL_DOWN);

		                asyncPrint("### Orderbook-based SELL executed ### Orderbook size: " +
		                   formatDecimal(orderBookSize) + getMarketSummary());
	                }
	            }

                if(TradeFlg == 3) {
                    Trade.INSTANCE.SetUpDownSecRcv(); 
                    Thread thread1 = new Thread(() -> {
                        long startTime = System.currentTimeMillis();
                        while(true){
                            Trade.INSTANCE.TradeStart(PROFIT);
                            int value = Trade.INSTANCE.GetUpDownSecRcv();
                            if (TRACE > 0) {
                                asyncPrint(String.format("[TRACE] Waiting for trade completion: %d", value));
                            }
                            if(value != 99 && value != 10000 && value == 1) {
                                Trade.INSTANCE.TradeStart(CLOSE);
                                TradeFlg = 0; // 거래 완료 후 상태 초기화
                                break;
                            }

                            if (System.currentTimeMillis() - startTime > 10000) {
                                Trade.INSTANCE.TradeStart(CLOSE);
                                TradeFlg = 0;
                                if (TRACE > 0) {
                                    asyncPrint("Trade timeout after 5 seconds - closing position");
                                }
                                break;
                            }

                            try {
                                Thread.sleep(50);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    });
                    
                    thread1.start();
                }
            }
        }
        
        // 가격 이력 업데이트 메서드
        private void updatePriceHistory(double currentSpotPrice, double currentFuturesPrice) {
            // --- 스팟 가격 이력 업데이트 ---
            if (currentSpotPrice > 0) { // 유효한 스팟 가격만 처리
                if (spotPriceHistory.size() < MAX_PRICE_HISTORY) {
                    // 이력이 아직 가득 차지 않았으면
                    if (spotPriceHistory.isEmpty()) {
                        // 첫 번째 데이터는 무조건 추가
                        spotPriceHistory.add(currentSpotPrice);
                        if (TRACE > 0) { // Changed from TRACE > 1
                            asyncPrint(String.format("Spot price %.4f added (first entry). History size: %d", currentSpotPrice, spotPriceHistory.size()));
                        }
                    } else {
                        // 두 번째부터는 이전 가격과 정수부 비교
                        double lastHistoricalSpotPrice = spotPriceHistory.get(spotPriceHistory.size() - 1);
                        if ((int)currentSpotPrice != (int)lastHistoricalSpotPrice) {
                            spotPriceHistory.add(currentSpotPrice);
                        }
                    }
                    // 이력이 방금 가득 찼는지 확인하고 로그 (이 위치는 변경 없음)
                    if (TRACE > 0 && spotPriceHistory.size() == MAX_PRICE_HISTORY) { 
                        asyncPrint(String.format("Spot price history now full (%d entries). Update logic will now apply.", MAX_PRICE_HISTORY));
                    }
                } else { // 이력이 이미 가득 찬 경우 (spotPriceHistory.size() == MAX_PRICE_HISTORY)
                    double lastHistoricalSpotPrice = spotPriceHistory.get(MAX_PRICE_HISTORY - 1);
                    if ((int)currentSpotPrice != (int)lastHistoricalSpotPrice) {
                        spotPriceHistory.remove(0); 
                        spotPriceHistory.add(currentSpotPrice); 
                    }
                }
            }

            // --- 선물 가격 이력 업데이트 (스팟과 동일한 통합 로직 적용) ---
            if (currentFuturesPrice > 0) { // 유효한 선물 가격만 처리
                if (futuresPriceHistory.size() < MAX_PRICE_HISTORY) {
                    if (futuresPriceHistory.isEmpty()) {
                        futuresPriceHistory.add(currentFuturesPrice);
                        if (TRACE > 0) { // Changed from TRACE > 1
                            asyncPrint(String.format("Futures price %.4f added (first entry). History size: %d", currentFuturesPrice, futuresPriceHistory.size()));
                        }
                    } else {
                        double lastHistoricalFuturesPrice = futuresPriceHistory.get(futuresPriceHistory.size() - 1);
                        if ((int)currentFuturesPrice != (int)lastHistoricalFuturesPrice) {
                            futuresPriceHistory.add(currentFuturesPrice);
                        }
                    }
                    if (TRACE > 0 && futuresPriceHistory.size() == MAX_PRICE_HISTORY) { 
                        asyncPrint(String.format("Futures price history now full (%d entries). Update logic will now apply.", MAX_PRICE_HISTORY));
                    }
                } else { // 이력이 이미 가득 찬 경우
                    double lastHistoricalFuturesPrice = futuresPriceHistory.get(MAX_PRICE_HISTORY - 1);
                    if ((int)currentFuturesPrice != (int)lastHistoricalFuturesPrice) {
                        futuresPriceHistory.remove(0); 
                        futuresPriceHistory.add(currentFuturesPrice); 
                    } 
                }
            }
        }

        // 가격 변동이 유의미한지 확인하는 메서드 (이전 가격과의 차이 평균 기반)
        private boolean isPriceFluctuationSignificant(boolean isBuySignal) {
            // MAX_PRICE_HISTORY 만큼 가격 정보가 쌓인 후에만 조건을 평가
            if (spotPriceHistory.size() < MAX_PRICE_HISTORY || futuresPriceHistory.size() < MAX_PRICE_HISTORY) {
                if (TRACE > 0) { // 상세 로그 추가
                    asyncPrint(String.format("PriceFluctuation: Skipped. History not full. Spot: %d/%d, Futures: %d/%d",
                        spotPriceHistory.size(), MAX_PRICE_HISTORY,
                        futuresPriceHistory.size(), MAX_PRICE_HISTORY));
                }
                return false; // 가격 정보가 충분히 쌓이지 않았으면 조건 미충족
            }

            if (oldtradePrice == -999999 || oldtradePriceF == -999999) {
                if (TRACE > 0) asyncPrint("Initial prices not set. Skipping fluctuation check.");
                return false;
            }

            // 1. 기존 가격 변동 조건 확인
            double avgAbsSpotDiff = (int)getAverageAbsoluteHistoricalDifference(spotPriceHistory);
            double avgAbsFuturesDiff = (int)getAverageAbsoluteHistoricalDifference(futuresPriceHistory);
            double currentSpotDiff = (int)(tradePrice - oldtradePrice);
            double currentFuturesDiff = (int)(tradePriceF -oldtradePriceF);
            
            if(avgAbsSpotDiff < 2)
            	avgAbsSpotDiff = 2;
            
            if(avgAbsFuturesDiff < 2)
            	avgAbsFuturesDiff = 2;

            if (TRACE > 0) {
                StringBuilder sb = new StringBuilder();
                sb.append(String.format("PriceFluctuation Check (isBuySignal: %b):", isBuySignal)).append("\n")
                  .append(String.format("  Spot: avgAbsDiff=%.4f, currentDiff=%.4f", avgAbsSpotDiff, currentSpotDiff)).append("\n")
                  .append(String.format("  Futures: avgAbsDiff=%.4f, currentDiff=%.4f", avgAbsFuturesDiff, currentFuturesDiff));
                asyncPrint(sb.toString());
            }

            int isMarketStable = 10;  
            
            boolean spotConditionMet = false;
            if (avgAbsSpotDiff < 1e-9) { 
                if (TRACE > 0) asyncPrint("  Spot: Using near-zero avgAbsDiff path.");
                if (isBuySignal && currentSpotDiff > 1e-9) { 
                    spotConditionMet = false;
                } else if (!isBuySignal && currentSpotDiff < -1e-9) { 
                    spotConditionMet = false;
                }
            } else { 
                if (TRACE > 0) asyncPrint(String.format("  Spot: Using %dx avgAbsDiff path (threshold: %.4f).", isMarketStable, isMarketStable * avgAbsSpotDiff));
                if (isBuySignal && currentSpotDiff > 0 && currentSpotDiff > isMarketStable * avgAbsSpotDiff) {
                    spotConditionMet = true;
                } else if (!isBuySignal && currentSpotDiff < 0 && Math.abs(currentSpotDiff) > isMarketStable * avgAbsSpotDiff) {
                    spotConditionMet = true;
                }
            }
            if (TRACE > 0) asyncPrint(String.format("  SpotConditionMet: %b", spotConditionMet));

            boolean futuresConditionMet = false;
            if (avgAbsFuturesDiff < 1e-9) {
                if (TRACE > 0) asyncPrint("  Futures: Using near-zero avgAbsDiff path.");
                if (isBuySignal && currentFuturesDiff > 1e-9) {
                    futuresConditionMet = false;
                } else if (!isBuySignal && currentFuturesDiff < -1e-9) {
                    futuresConditionMet = false;
                }
            } else {
                if (TRACE > 0) asyncPrint(String.format("  Futures: Using %dx avgAbsDiff path (threshold: %.4f).", isMarketStable, isMarketStable * avgAbsFuturesDiff));
                if (isBuySignal && currentFuturesDiff > 0 && currentFuturesDiff > isMarketStable * avgAbsFuturesDiff) {
                    futuresConditionMet = true;
                } else if (!isBuySignal && currentFuturesDiff < 0 && Math.abs(currentFuturesDiff) > isMarketStable * avgAbsFuturesDiff) {
                    futuresConditionMet = true;
                }
            }
            if (TRACE > 0) asyncPrint(String.format("  FuturesConditionMet: %b", futuresConditionMet));
            
            // 2. 기존 조건이 충족되면 반등 확률 검사
            return spotConditionMet || futuresConditionMet;
 
        }

        // 과거 가격 변동의 절대값 평균을 계산하는 헬퍼 메서드
        // (변동이 0인 경우는 평균 계산에서 제외)
        private double getAverageAbsoluteHistoricalDifference(List<Double> priceHistory) {
            int numHistoricalPricePoints = priceHistory.size();
            // 최소 2개의 가격 데이터가 있어야 의미 있는 차이를 계산할 수 있습니다.
            if (numHistoricalPricePoints < 2) {
                return 0.0; // 충분한 데이터가 없으면 0 반환
            }

            double sumAbsoluteNonZeroDifferences = 0.0;
            int countNonZeroDifferences = 0;

            // 모든 연속된 가격 쌍의 차이를 계산합니다 (마지막 데이터까지 포함)
            // 예를 들어, 가격 이력이 [P0, P1, P2, P3, P4] (size=5) 라면,
            // 루프는 P0,P1 -> P1,P2 -> P2,P3 -> P3,P4의 모든 차이를 계산합니다.
            for (int i = 0; i < numHistoricalPricePoints - 1; i++) {
                double previousPrice = priceHistory.get(i);
                double currentPrice = priceHistory.get(i + 1);
                double absoluteDifference = Math.abs(currentPrice - previousPrice);

                // 변동이 0이 아닌 경우에만 합계 및 카운트에 포함
                if (absoluteDifference > 1e-9) { // 1e-9는 매우 작은 값 (0에 가까움)
                    sumAbsoluteNonZeroDifferences += absoluteDifference;
                    countNonZeroDifferences++;
                }
            }

            if (countNonZeroDifferences == 0) {
                return 0.0; // 모든 과거 변동이 0이었음
            } else {
                return sumAbsoluteNonZeroDifferences / countNonZeroDifferences;
            }
        }
    } // End of MessageHandler class
    
    public static void InitMain() {
        try {
            if (REFRESH_CNT > 20) {
                Trade.INSTANCE.TradeStart(REFRESH);
                REFRESH_CNT = 0;
                //Trade.INSTANCE.TradeStart(TIME_RESET);
                Thread.sleep(5000);
            }
            REFRESH_CNT++;
            // 1. 전체 거래량 계산 (최근 10개 1분봉 기준 평균)
            volume = 0; // 선물
            volumeBTC = 0; // 스팟
            List<Candlestick> candlestickListF = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20,
                    null, null);
            List<Candlestick> candlestickListS = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20,
                    null, null);
            Candlestick candlestickF = null;
            List<Double> priceListF = new ArrayList<>(); // 선물 종가 리스트 (볼린저밴드용)
            int countF = 0;
            
            if (candlestickListF != null && !candlestickListF.isEmpty()) {
                for (int i = 0; i < candlestickListF.size(); i++) {
                    candlestickF = candlestickListF.get(i);
                    // 마지막 캔들 제외, 최근 10개 캔들 (인덱스 9~18)
                    if (i >= candlestickListF.size() - 6 && i < candlestickListF.size() - 1) {
                        volume += Double.parseDouble(candlestickF.getVolume());
                        countF++;
                    }
                    priceListF.add(Double.parseDouble(candlestickF.getClose()));
                }
            }
            if (countF > 0)
                volume = volume / countF;
            volume = roundToTwoDecimalPlaces(volume);

            Candlestick candlestickS = null;
            List<Double> priceListS = new ArrayList<>(); // 스팟 종가 리스트 (볼린저밴드용)
            int countS = 0;
            
            if (candlestickListS != null && !candlestickListS.isEmpty()) {
                for (int i = 0; i < candlestickListS.size(); i++) {
                    candlestickS = candlestickListS.get(i);
                    if (i >= candlestickListS.size() - 6 && i < candlestickListS.size() - 1) {
                        volumeBTC += Double.parseDouble(candlestickS.getVolume());
                        countS++;
                    }
                    priceListS.add(Double.parseDouble(candlestickS.getClose()));
                }
                
            }
            if (countS > 0)
                volumeBTC = volumeBTC / countS;
            volumeBTC = roundToTwoDecimalPlaces(volumeBTC);
            
            BollCheck = 0; 
            if (!priceListS.isEmpty() && priceListS.size() >= 20) { 
                BollingerBands bollS = new BollingerBands(priceListS, 20, 2.0);
                BollDown = bollS.getdownBollingerBands();
                BollUp = bollS.getUpBollingerBands();
                if (!candlestickListS.isEmpty()) {
                    Candlestick lastCandlestickS = candlestickListS.get(candlestickListS.size() - 1);
                    if (BollUp > 0 && BollDown > 0) { 
                        double closePrice = Double.parseDouble(lastCandlestickS.getClose());
                        if (closePrice > BollUp || closePrice < BollDown) {
                            BollCheck = 1;
                        }
                    }
                }
            } else {
                BollUp = 0; BollDown = 0;
                if (TRACE > 0 && priceListS.size() < 20) asyncPrint("Not enough data for Bollinger Bands calculation: " + priceListS.size() + " points.");
            }


            // 2. 설정 파일 로드
            String filePath = "c:/Trade/Capitalcore.properties";
            try (InputStream input = new FileInputStream(filePath)) {
                Properties prop = new Properties();
                prop.load(input);

                // 기존 설정값 로드
                orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1", "0.001"));
                orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1", "1"));
                orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2", "4"));
                orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2", "10"));

                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
                trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
                checkboll = Integer.parseInt(prop.getProperty("checkboll", "0")); // 볼린저밴드 체크 활성화
                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
                TRACE = Integer.parseInt(prop.getProperty("TRACE", "0")); // 로그 레벨

            } catch (IOException | NumberFormatException e) {
                asyncPrint("Error loading or parsing configuration file: " + e.getMessage());
                // 기본값 유지 또는 오류 처리
            }

            // 로그 출력 (TRACE 레벨에 따라)
            asyncPrint("--- Initial Settings and Status ---");
            asyncPrint("Orderbook Parameters (S1/E1/S2/E2): " + orderbook_S1 + "/" + orderbook_E1 + "/"
                + orderbook_S2 + "/"
                + orderbook_E2);
            asyncPrint("Total Volume Limits (S/F): " + volumeBTC_Check + "/" + trade_volume_max);
            asyncPrint("Current Average Volume (S/F): " + volumeBTC + "/" + volume);
            asyncPrint("Bollinger Bands (Lower/Upper/Check/Enabled): " + roundToTwoDecimalPlaces(BollDown) + "/"
                    + roundToTwoDecimalPlaces(BollUp) + "/" + BollCheck + "/" + checkboll);
            asyncPrint("Other (INIT_WAIT/TRACE): " + INIT_WAIT + "/" + TRACE);
            asyncPrint("--------------------------");
            Trade.INSTANCE.SetUpDownSecRcv(); 
            Trade.INSTANCE.TradeStart(CHECK);
            Thread.sleep(1000);
            int value = Trade.INSTANCE.GetUpDownSecRcv();
            if (TRACE > 0) {
                asyncPrint(String.format("[TRACE] Initial check value: %d", value));
            }
            if(value != 1)
                INIT_WAIT = 0; // 초기화 대기 시간 설정값이 0으로 변경됨

        } catch (Exception e) { // Binance API 호출 등에서 발생 가능
            asyncPrint("Error during InitMain execution: " + e.getMessage());
            e.printStackTrace();
            volume = 0; // 오류 시 초기화
            volumeBTC = 0;
            BollCheck = 0;
        }
        
    }

    public static void main(String[] args) throws InterruptedException, IOException {
        // 바이낸스 클라이언트 생성 (API 키 필요 시 입력)
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();

        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();

        // 트레이딩 UI 실행 (추정)
        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP); // 창 상단 고정?
        Trade.INSTANCE.TradeStart(TIME_RESET); // 시간 리셋?

        asyncPrint("Waiting for initialization (10 seconds)...");
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            e.printStackTrace();
        }
        asyncPrint("Initialization wait complete.");

        // 메시지 핸들러 인스턴스 생성 및 스레드 시작
        MessageHandler messageHandlerInstance = new MessageHandler(); // Create instance first
        Thread messageHandlerThread = new Thread(messageHandlerInstance); // Pass instance to thread constructor
        messageHandlerThread.setName("MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");

        asyncPrint("Main thread waiting started.");
        // 메인 스레드는 무한 대기 또는 다른 작업 수행
        while (true) {
            try {
                Thread.sleep(60000); // 1분마다 상태 출력 또는 확인
                // asyncPrint("메인 스레드 대기 중...");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                asyncPrint("Main thread interrupted. Shutting down...");
                // 필요시 하위 스레드 종료 로직 추가
                messageProcessingService.shutdownNow(); // 즉시 종료 시도
                PrintService.shutdownNow();

                // Wait for termination with timeout
                awaitTermination(messageProcessingService, "messageProcessingService");
                awaitTermination(PrintService, "PrintService");

                messageHandlerThread.interrupt(); // Interrupt the main handler thread
                // messageHandler1Thread.interrupt(); // Uncomment if MessageHandler1 is used
                break;
            }
        }
        asyncPrint("Main thread terminated.");
    }

    // Helper method for executor termination
    private static void awaitTermination(ExecutorService executor, String name) {
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                System.err.println(name + " did not terminate gracefully after shutdown(). Forcing shutdownNow().");
                List<Runnable> droppedTasks = executor.shutdownNow();
                System.err.println(name + " dropped " + droppedTasks.size() + " tasks.");
                // Wait a little longer for tasks to respond to cancellation
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    System.err.println(name + " did not terminate even after shutdownNow().");
                } else {
                    System.out.println(name + " terminated after shutdownNow().");
                }
            } else {
                System.out.println(name + " terminated gracefully.");
            }
        } catch (InterruptedException e) {
            System.err.println("Interrupted while waiting for " + name + " termination. Forcing shutdownNow().");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private static double roundToTwoDecimalPlaces(double value) {
        // 소수점 4자리까지 반올림 (가격 등에 사용될 수 있으므로 정밀도 유지)
        DecimalFormat df_local = new DecimalFormat("#.####");
        // DecimalFormat은 스레드 안전하지 않으므로 지역 변수로 사용하거나 동기화 필요
        // 여기서는 지역 변수로 사용
        try {
            return Double.parseDouble(df_local.format(value));
        } catch (NumberFormatException e) {
            return value; // 오류 시 원본 값 반환
        }
    }
}
