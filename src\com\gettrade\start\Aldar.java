package com.gettrade.start;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.net.Socket;
import java.text.DecimalFormat;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.stream.JsonReader;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class Aldar {

    public enum TradeAction {
        UP,
        DOWN,
        HOLD
    }

    public interface Trade extends Library {
        Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

        public void TradeBitqDlg_expert();
        public int GetUpDownSecRcv();
        public int SetUpDownSecRcv();
        public void TradeStart(int nflag, String value);
    }

    public static int BUY_UP = 500301;
    public static int SELL_DOWN = 500302;
    public static int CHECK = 500303;
    public static int GETTIME = 500305;
    public static int WINDOWS_TOP = 300008;

    // --- 기술적 분석을 위한 데이터 개수 ---
    public static final int CANDLESTICK_DATA_COUNT = 30;

    public static int TIME_RESET = 4;
    public static int REFRESH = 304;
    public static int TRADE_STOP = 103;
    public static volatile int REFRESH_CNT = 0;
    public static volatile double volume = 0;
    public static volatile double volumeBTC = 0;
    public static volatile double orderbook_S1 = 0.001;
    public static volatile double orderbook_E1 = 1.5;
    public static volatile double orderbook_S2 = 1.5;
    public static volatile double orderbook_E2 = 1.5;
    public static volatile double volumeBTC_Check = 15;
    public static volatile double trade_volume_max = 15;

    public static volatile int TradeFlg = 3;
    public static volatile int INIT_WAIT = 0;
    public static volatile int TRACE = 0;
    public static volatile String MONEY = "2";
    
    
    public static volatile double currentSpotPrice = 0.0; // 실시간 현물가
    public static volatile double currentFuturesPrice = 0.0; // 실시간 선물가
    
    public static BinanceFuturesApiClientFactory BinancefactoryF = null;
    public static BinanceApiFuturesRestClient clientF = null;

    public static BinanceSpotApiClientFactory factory = null;
    public static BinanceApiSpotRestClient client = null;

    public static String symbol = "BTCUSDT";
    public static volatile List<Double> priceListS_global = new CopyOnWriteArrayList<>();
    private static final Object volumeLock = new Object();
    private static final ExecutorService messageProcessingService = Executors.newFixedThreadPool(10);
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();

    public static void asyncPrint(String message) {
        PrintService.submit(() -> System.out.println(message));
    }

    // 오더북 스냅샷 클래스 - 20초간 오더북 데이터 수집 및 분석용
    public static class OrderbookSnapshot {
        final long timestamp;
        final double spotBidVolume;
        final double spotAskVolume;
        final double futuresBidVolume;
        final double futuresAskVolume;
        final double spotPrice;
        final double futuresPrice;

        public OrderbookSnapshot(long timestamp, double spotBidVolume, double spotAskVolume, 
                               double futuresBidVolume, double futuresAskVolume, 
                               double spotPrice, double futuresPrice) {
            this.timestamp = timestamp;
            this.spotBidVolume = spotBidVolume;
            this.spotAskVolume = spotAskVolume;
            this.futuresBidVolume = futuresBidVolume;
            this.futuresAskVolume = futuresAskVolume;
            this.spotPrice = spotPrice;
            this.futuresPrice = futuresPrice;
        }
        
        // 스팟 오더북 불균형 계산
        public double getSpotImbalance() {
            if (spotBidVolume + spotAskVolume == 0) return 0.0;
            return (spotBidVolume - spotAskVolume) / (spotBidVolume + spotAskVolume);
        }
        
        // 선물 오더북 불균형 계산
        public double getFuturesImbalance() {
            if (futuresBidVolume + futuresAskVolume == 0) return 0.0;
            return (futuresBidVolume - futuresAskVolume) / (futuresBidVolume + futuresAskVolume);
        }
        
        // 전체 오더북 불균형 계산 (선물 시장에 더 높은 가중치)
        public double getTotalImbalance() {
            double spotImbalance = getSpotImbalance();
            double futuresImbalance = getFuturesImbalance();
            double totalImbalance = (spotImbalance * 0.3) + (futuresImbalance * 0.7);
            
            if (TRACE > 0) {
                asyncPrint(String.format("Imbalance Calc: Spot: %.4f, Futures: %.4f, Total: %.4f", 
                    spotImbalance, futuresImbalance, totalImbalance));
            }
            
            return totalImbalance;
        }
    }

    // --- MessageHandler (Main Trading Logic) ---
    static class MessageHandler implements Runnable {
        // --- 오더북 분석 기반 거래 파라미터 (완화된 조건) ---
        private static final int ORDERBOOK_ANALYSIS_SECONDS = 20; // 거래 진행 20초 전까지 데이터 수집
        private static final double MIN_ORDERBOOK_IMBALANCE = 0.08; // 최소 오더북 불균형 임계값 (15% -> 8%로 완화)
        
        // 오더북 데이터 수집을 위한 저장소
        private final List<OrderbookSnapshot> orderbookHistory = new ArrayList<>();

        private volatile double tradeOrderbookDownF20 = Double.NaN;
        private volatile double tradeOrderbookUpF20 = Double.NaN;
        private volatile double tradeOrderbookDown20 = Double.NaN;
        private volatile double tradeOrderbookUp20 = Double.NaN;

        private volatile boolean bfirstBTC20 = false;
        private volatile boolean bfirstFBTC20 = false;
        
        @Override
        public void run() {
            asyncPrint("🔄 Starting MessageHandler - waiting for orderbook data from 127.0.0.1:22222");
            int reconnectAttempts = 0;
            
            while (true) {
                try (Socket socket = new Socket("127.0.0.1", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    asyncPrint("✅ Orderbook data socket connection successful.");
                    reconnectAttempts = 0; // Reset counter on successful connection
                    resetDataFlags();
                    resetTradeOrderbook();

                    while (true) {
                        String message = in.readLine();
                        if (message != null && !message.isEmpty() && INIT_WAIT == 1 && TradeFlg == 0) {
                            try {
                                processMessage(message);
                            } catch (Exception e) {
                                asyncPrint("Error occurred during message processing: " + e.getMessage());
                                if (TRACE > 0) e.printStackTrace();
                            } 
                        }
                    }
                } catch (IOException e) {
                    reconnectAttempts++;
                    asyncPrint("❌ Socket connection error (attempt " + reconnectAttempts + "): " + e.getMessage());
                    if (reconnectAttempts >= 5) {
                        asyncPrint("🚨 Failed to connect after 5 attempts. Please check if orderbook data server is running on 127.0.0.1:22222");
                    }
                    resetTradeOrderbook();
                    resetDataFlags();
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        asyncPrint("Interrupt occurred during reconnection wait.");
                    }
                } catch (Exception ex) {
                    asyncPrint("Unexpected error in MessageHandler loop: " + ex.getMessage());
                    ex.printStackTrace();
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }

        private void processMessage(String message) {
            if (message == null || message.isEmpty()) {
                asyncPrint("Empty message received, skipping processing.");
                return;
            }

            // Temporarily suspend trading when trading volume is exceeded
            if (volume > trade_volume_max || volumeBTC > volumeBTC_Check) {
                if (TRACE > 0)
                  asyncPrint("Total trading volume exceeded (Futures:" + volume + ", Spot:" + volumeBTC
                    + "), temporarily suspending trading. TradeFlg=3");
                  TradeFlg = 3; 
            }

            try (JsonReader reader = new JsonReader(new StringReader(message))) {
                reader.setLenient(true);
                while (reader.hasNext()) {
                    JsonObject jsonObject = JsonParser.parseReader(reader).getAsJsonObject();

                    if (jsonObject.has("Mtype") && jsonObject.has("message")) {
                        String mtype = jsonObject.get("Mtype").getAsString();
                        String msg = jsonObject.get("message").getAsString();

                        try {
                            if ("BTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-",3);
                                if (validateOrderbookData(orderbook20MSg)) {
                                    tradeOrderbookDown20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUp20 = Double.parseDouble(orderbook20MSg[1]);
                                    currentSpotPrice = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstBTC20 = true;
                                } else {
                                    asyncPrint("Invalid or incomplete spot data: " + msg);
                                }
                            } else if ("FBTC20".equals(mtype)) {
                                String[] orderbook20MSg = msg.split("-",3);
                                if (validateOrderbookData(orderbook20MSg)) {
                                    tradeOrderbookDownF20 = Double.parseDouble(orderbook20MSg[0]);
                                    tradeOrderbookUpF20 = Double.parseDouble(orderbook20MSg[1]);
                                    currentFuturesPrice = Double.parseDouble(orderbook20MSg[2]);
                                    bfirstFBTC20 = true;
                                } else {
                                    asyncPrint("Invalid or incomplete futures data: " + msg);
                                }
                            }
                        } catch (NumberFormatException e) {
                            asyncPrint("Number format error during data parsing: " + msg + " / Error: " + e.getMessage());
                            continue; 
                        } catch (Exception e) {
                            asyncPrint("Exception occurred during data processing: " + msg + " / Error: " + e.getMessage());
                            continue;
                        }

                        // 현물과 선물 데이터가 모두 수신되면 오더북 분석 진행
                        if (bfirstBTC20 && bfirstFBTC20) {
                            if (Double.isNaN(tradeOrderbookUp20) || Double.isNaN(tradeOrderbookDown20) ||
                                Double.isNaN(tradeOrderbookUpF20) || Double.isNaN(tradeOrderbookDownF20)) {
                                continue; 
                            }

                            if(TradeFlg == 0 && INIT_WAIT > 0) {
                                // 오더북 데이터 수집 및 분석
                                collectOrderbookData();
                                makeOrderbookBasedTradeDecision();
                            }
                        }
                    }
                }
            } catch (Exception e) {
                if (TRACE > 0) {
                    asyncPrint("Message JSON parsing error: '" + message + "'. Error: " + e.getMessage());
                }
            }
        }

        /**
         * Collect orderbook data (for 20 seconds)
         */
        private void collectOrderbookData() {
            long currentTime = System.currentTimeMillis();
            
            if (TRACE > 0) {
                asyncPrint(String.format("Creating snapshot: Spot Vol(B/A): %.2f/%.2f, Futures Vol(B/A): %.2f/%.2f", 
                    tradeOrderbookDown20, tradeOrderbookUp20, tradeOrderbookDownF20, tradeOrderbookUpF20));
            }

            // Create new orderbook snapshot
            OrderbookSnapshot snapshot = new OrderbookSnapshot(
                currentTime,
                tradeOrderbookDown20,   // Spot bid volume
                tradeOrderbookUp20,     // Spot ask volume
                tradeOrderbookDownF20,  // Futures bid volume
                tradeOrderbookUpF20,    // Futures ask volume
                currentSpotPrice,       // Spot price
                currentFuturesPrice     // Futures price
            );
            
            // Filter out invalid data
            if (!isValidOrderbookSnapshot(snapshot)) {
                if (TRACE > 0) asyncPrint("Invalid orderbook snapshot filtered out");
                return;
            }
            
            // Add to orderbook history
            synchronized (orderbookHistory) {
                orderbookHistory.add(snapshot);
                
                // Remove data older than 20 seconds
                long cutoffTime = currentTime - (ORDERBOOK_ANALYSIS_SECONDS * 1000);
                orderbookHistory.removeIf(data -> data.timestamp < cutoffTime);
            }
        }

        /**
         * 오더북 분석 기반 거래 결정
         */
        private void makeOrderbookBasedTradeDecision() {
            synchronized (orderbookHistory) {
                if (orderbookHistory.size() < 5) {
                    if (TRACE > 0) asyncPrint("Insufficient orderbook data (Current: " + orderbookHistory.size() + ", Required: 5 or more)");
                    return;
                }

                OrderbookAnalysisResult analysis = analyzeOrderbookTrend();
                
                if (TRACE > 0) {
                    asyncPrint(String.format("Orderbook analysis result - Direction: %s, Confidence: %.3f, Imbalance trend: %.3f", 
                             analysis.direction, analysis.confidence, analysis.imbalanceTrend));
                }
                
                // 거래 조건 완화 및 상세 로깅 추가
                double confidenceThreshold = 0.4; // 기존 0.5에서 완화
                double imbalanceThreshold = 0.05; // 기존 0.08 (MIN_ORDERBOOK_IMBALANCE) 에서 완화

                boolean confidenceMet = analysis.confidence >= confidenceThreshold;
                boolean imbalanceMet = Math.abs(analysis.imbalanceTrend) >= imbalanceThreshold;
                boolean directionMet = !analysis.direction.equals("HOLD");

                if (confidenceMet && imbalanceMet && directionMet) {
                    double tradeAmount = Math.min(Double.parseDouble(MONEY), trade_volume_max);

                    // 거래량이 0보다 클 때만 거래 실행
                    if (tradeAmount <= 0) {
                        if (TRACE > 0) asyncPrint("Trade amount is zero or less, skipping trade.");
                        return; // 거래 중단
                    }
                    boolean isBuySignal = analysis.direction.equals("UP");
                        
                    if (TRACE > 0) {
                        asyncPrint(String.format("✅ TRADE SIGNAL: Direction: %s, Confidence: %.2f%%, Volume: %.2f", 
                                 analysis.direction, analysis.confidence * 100, tradeAmount));
                    }        
                    executeTrade(isBuySignal, tradeAmount);
                } else {
                    if (TRACE > 0) {
                        String reason = "";
                        if (!confidenceMet) reason += String.format("Confidence not met (%.3f < %.3f) ", analysis.confidence, confidenceThreshold);
                        if (!imbalanceMet) reason += String.format("Imbalance trend not met (%.3f < %.3f) ", Math.abs(analysis.imbalanceTrend), imbalanceThreshold);
                        if (!directionMet) reason += "Direction is HOLD ";
                        asyncPrint("❌ No trade: " + reason.trim());
                    }
                }
            }
        }

        /**
         * 오더북 분석 결과 클래스
         */
        private static class OrderbookAnalysisResult {
            final String direction;      // UP, DOWN, HOLD
            final double confidence;     // 0.0 ~ 1.0
            final double imbalanceTrend; // -1.0 ~ 1.0
            
            public OrderbookAnalysisResult(String direction, double confidence, double imbalanceTrend) {
                this.direction = direction;
                this.confidence = confidence;
                this.imbalanceTrend = imbalanceTrend;
            }
        }

        /**
         * 오더북 추세 분석 (개선된 로직)
         */
        private OrderbookAnalysisResult analyzeOrderbookTrend() {
            if (orderbookHistory.size() < 5) {
                return new OrderbookAnalysisResult("HOLD", 0.0, 0.0);
            }

            List<Double> imbalanceValues = new ArrayList<>();
            List<Double> volumeTrends = new ArrayList<>();
            
            for (OrderbookSnapshot snapshot : orderbookHistory) {
                imbalanceValues.add(snapshot.getTotalImbalance());
                
                double spotVolumeRatio = calculateSafeVolumeRatio(snapshot.spotBidVolume, snapshot.spotAskVolume);
                double futuresVolumeRatio = calculateSafeVolumeRatio(snapshot.futuresBidVolume, snapshot.futuresAskVolume);
                volumeTrends.add((spotVolumeRatio * 0.3) + (futuresVolumeRatio * 0.7));
            }
            
            // 1. 핵심 지표 계산
            double imbalanceTrend = calculateTrendSlope(imbalanceValues); // 불균형 추세
            double volumeTrendSlope = calculateTrendSlope(volumeTrends); // 거래량 추세
            double trendConsistency = calculateTrendConsistency(imbalanceValues); // 추세 일관성
            double recentImbalanceAvg = imbalanceValues.stream()
                .skip(Math.max(0, imbalanceValues.size() - 5))
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0); // 최근 불균형 평균

            // 2. 통합 신호 강도 계산 (Improved Logic)
            // 각 요인의 가중치를 정의하여 신호 강도를 종합적으로 판단
            double trendWeight = 0.5;       // 불균형 추세의 가중치
            double recentAvgWeight = 0.3;   // 최근 평균의 가중치
            double volumeWeight = 0.2;      // 거래량 추세의 가중치

            // 원시 신호 점수: 양수면 상승 신호, 음수면 하락 신호
            double rawSignal = (imbalanceTrend * trendWeight) + (recentImbalanceAvg * recentAvgWeight) + (volumeTrendSlope * volumeWeight);

            // 3. 신뢰도 및 거래 방향 결정 (Improved Logic)
            // tanh 함수를 사용해 신호 강도를 -1에서 1 사이로 정규화. 증폭 계수(e.g., 1.5)를 곱해 민감도 조절
            double normalizedSignal = Math.tanh(rawSignal * 1.5);
            
            // 최종 신뢰도: 정규화된 신호의 크기(절대값)에 추세 일관성을 곱하여 신뢰도 보정
            double finalConfidence = Math.abs(normalizedSignal) * trendConsistency;

            String direction = "HOLD";
            double signalThreshold = 0.20; // 거래를 실행할 최소 신호 강도 임계값 (기존 0.05보다 높여 불필요한 거래 방지)

            if (normalizedSignal > signalThreshold) {
                direction = "UP";
            } else if (normalizedSignal < -signalThreshold) {
                direction = "DOWN";
            }

            // 거래 방향이 HOLD일 경우, 신뢰도를 0으로 설정
            if (direction.equals("HOLD")) {
                finalConfidence = 0.0;
            }

            return new OrderbookAnalysisResult(direction, finalConfidence, imbalanceTrend);
        }

        /**
         * Safe volume ratio calculation to avoid distortion from small volumes
         */
        private double calculateSafeVolumeRatio(double bidVolume, double askVolume) {
            // If both volumes are very small (< 0.1), the data is unreliable, treat as neutral
            if (bidVolume < 0.1 && askVolume < 0.1) {
                return 1.0; // Neutral ratio
            }
            
            // Add a small epsilon to avoid division by zero, especially if one volume is zero
            double epsilon = 1e-9;
            return (bidVolume + epsilon) / (askVolume + epsilon);
        }

        /**
         * 추세 기울기 계산 (선형 회귀)
         */
        private double calculateTrendSlope(List<Double> values) {
            if (values.size() < 2) return 0.0;
            
            int n = values.size();
            double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
            
            for (int i = 0; i < n; i++) {
                double x = i;
                double y = values.get(i);
                sumX += x;
                sumY += y;
                sumXY += x * y;
                sumXX += x * x;
            }
            
            double denominator = n * sumXX - sumX * sumX;
            if (Math.abs(denominator) < 1e-9) {
                return 0.0; // Prevent division by zero or floating point instability
            }
            
            return (n * sumXY - sumX * sumY) / denominator;
        }

        /**
         * 추세 일관성 계산 (표준 편차 기반)
         */
        private double calculateTrendConsistency(List<Double> values) {
            if (values.size() < 2) return 0.0;

            // 1. Calculate the mean
            double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

            // 2. Calculate the standard deviation
            double stdDev = Math.sqrt(
                values.stream()
                      .mapToDouble(d -> Math.pow(d - mean, 2))
                      .average().orElse(0.0)
            );

            // 3. Calculate consistency based on volatility
            // We use the inverse of standard deviation, normalized to a 0-1 range.
            // A small constant is added to avoid division by zero and to scale the output.
            // The tanh function helps to map the value to a smooth 0-1 range.
            double consistency = Math.tanh(1.0 / (stdDev + 0.1)); 

            return consistency;
        }


        private boolean validateOrderbookData(String[] data) {
            if (data.length < 3) return false;
            try {
                for (int i = 0; i < 3; i++) {
                    double value = Double.parseDouble(data[i]);
                    if (value < 0 || Double.isNaN(value)) {
                        return false;
                    }
                }
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }

        private void resetTradeOrderbook() {
            tradeOrderbookDownF20 = Double.NaN;
            tradeOrderbookUpF20 = Double.NaN;
            tradeOrderbookDown20 = Double.NaN;
            tradeOrderbookUp20 = Double.NaN;
        }

        private void resetDataFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
            // 오더북 히스토리도 초기화
            synchronized(orderbookHistory) {
                orderbookHistory.clear();
            }
        }

        private boolean executeTrade(boolean isBuySignal, double amount) {
            String tradeAmount = String.format("%.2f", amount);
            if (TradeFlg == 0 && INIT_WAIT > 0) {
                TradeFlg = 3;
                if (isBuySignal) {
                    Trade.INSTANCE.TradeStart(BUY_UP, tradeAmount);
                    asyncPrint(String.format("### BUY trade executed: Amount %s, Time %s ###", tradeAmount, new java.util.Date()));
                    return true;
                } else {
                    Trade.INSTANCE.TradeStart(SELL_DOWN, tradeAmount);
                    asyncPrint(String.format("### SELL trade executed: Amount %s, Time %s ###", tradeAmount, new java.util.Date()));
                    return true;
                }
            } else {
                if (TRACE > 0) asyncPrint("Trade execution failed: TradeFlg=" + TradeFlg + ", INIT_WAIT=" + INIT_WAIT);
            }
            return false;
        }

        /**
         * Filter out outliers from orderbook data to improve analysis quality
         */
        private boolean isValidOrderbookSnapshot(OrderbookSnapshot snapshot) {
            // Check for extremely unusual volume ratios that might indicate data errors
            double spotRatio = snapshot.spotBidVolume / (snapshot.spotAskVolume + 0.001);
            double futuresRatio = snapshot.futuresBidVolume / (snapshot.futuresAskVolume + 0.001);
            
            // Filter out extreme ratios (beyond 100:1 or 1:100)
            if (spotRatio > 100 || spotRatio < 0.01 || futuresRatio > 100 || futuresRatio < 0.01) {
                return false;
            }
            
            // Check for reasonable price ranges (within 5% of previous values)
            if (orderbookHistory.size() > 0) {
                OrderbookSnapshot lastSnapshot = orderbookHistory.get(orderbookHistory.size() - 1);
                double spotPriceChange = Math.abs(snapshot.spotPrice - lastSnapshot.spotPrice) / lastSnapshot.spotPrice;
                double futuresPriceChange = Math.abs(snapshot.futuresPrice - lastSnapshot.futuresPrice) / lastSnapshot.futuresPrice;
                
                if (spotPriceChange > 0.05 || futuresPriceChange > 0.05) {
                    return false; // Price changed more than 5% in one snapshot
                }
            }
            
            return true;
        }
    }


    public static void InitMain() {
        try {
            REFRESH_CNT++;
            double localVolume = 0;
            double localVolumeBTC = 0;
            List<Candlestick> candlestickListF = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, CANDLESTICK_DATA_COUNT, null, null);
            List<Candlestick> candlestickListS = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, CANDLESTICK_DATA_COUNT, null, null);
            
            List<Double> priceListF = new ArrayList<>();
            int countF = 0;
            if (candlestickListF != null && !candlestickListF.isEmpty()) {
                for (int i= 0; i < candlestickListF.size(); i++) {
                    Candlestick candlestickF = candlestickListF.get(i);
                    // Use 10 candles for a more stable average volume
                    if (i >= candlestickListF.size() - 11 && i < candlestickListF.size() - 1) {
                        localVolume += Double.parseDouble(candlestickF.getVolume());
                        countF++;
                    }
                    priceListF.add(Double.parseDouble(candlestickF.getClose()));
                }
            }
            if (countF > 0) localVolume = localVolume / countF;
            localVolume = roundToTwoDecimalPlaces(localVolume);

            List<Double> newPriceListS = new ArrayList<>();
            int countS = 0;
            if (candlestickListS != null && !candlestickListS.isEmpty()) {
                for (int i = 0; i < candlestickListS.size(); i++) {
                    Candlestick candlestickS = candlestickListS.get(i);
                    // Use 10 candles for a more stable average volume
                    if (i >= candlestickListS.size() - 11 && i < candlestickListS.size() - 1) {
                        localVolumeBTC += Double.parseDouble(candlestickS.getVolume());
                        countS++;
                    }
                    newPriceListS.add(Double.parseDouble(candlestickS.getClose()));
                }
            }
            priceListS_global = new CopyOnWriteArrayList<>(newPriceListS);
            if (countS > 0) localVolumeBTC = localVolumeBTC / countS;
            localVolumeBTC = roundToTwoDecimalPlaces(localVolumeBTC);

            synchronized (volumeLock) {
                volume = localVolume;
                volumeBTC = localVolumeBTC;
            }
            
            String filePath = "c:/Trade/Aldar.properties";
            try (InputStream input = new FileInputStream(filePath)) {
                Properties prop = new Properties();
                prop.load(input);
                orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1", "0.001"));
                orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1", "1"));
                orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2", "4"));
                orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2", "10"));
                volumeBTC_Check = Double.parseDouble(prop.getProperty("volumeBTC_Check", "15"));
                trade_volume_max = Double.parseDouble(prop.getProperty("trade_volume_max", "15"));
                INIT_WAIT = Integer.parseInt(prop.getProperty("INIT_WAIT", "0"));
                TRACE = Integer.parseInt(prop.getProperty("TRACE", "0")); 
                MONEY = prop.getProperty("MONEY", "2");
            } catch (IOException | NumberFormatException e) {
                asyncPrint("Error loading or parsing configuration file: " + e.getMessage());
            }

            if (TRACE > 0) {
                asyncPrint("--- Orderbook Analysis-Based Trading Settings ---");
                asyncPrint(String.format("Orderbook parameters (S1/E1/S2/E2): %.3f/%.1f/%.1f/%.1f", orderbook_S1, orderbook_E1, orderbook_S2, orderbook_E2));
                asyncPrint(String.format("Volume limits (Spot/Futures): %.1f/%.1f", volumeBTC_Check, trade_volume_max));
                asyncPrint(String.format("Current average volume (Spot/Futures): %.2f/%.2f", volumeBTC, volume));
                asyncPrint("Trade amount: " + MONEY);
                asyncPrint("※ Collect orderbook data for 20 seconds, analyze imbalance trends, then proceed with trading");
                asyncPrint("--------------------------");
            }
        } catch (Exception e) { 
            asyncPrint("Error during InitMain execution: " + e.getMessage());
            if (TRACE > 1) e.printStackTrace();
            synchronized (volumeLock) {
                volume = 0;
                volumeBTC = 0;
            }
        }
    }

    public static void main(String[] args) throws InterruptedException, IOException {
        // Binance API 클라이언트 초기화 (API 키 설정 필요)
        BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
        clientF = BinancefactoryF.newRestClient();
        factory = BinanceAbstractFactory.createSpotFactory("", "");
        client = factory.newRestClient();
        
        // API 키 확인 경고
        asyncPrint("⚠️  WARNING: Binance API keys are not set. Please configure your API keys for live trading.");
        asyncPrint("📋 To set API keys, modify the createFuturesFactory() and createSpotFactory() calls with your actual keys.");

        Trade.INSTANCE.TradeBitqDlg_expert();
        Trade.INSTANCE.TradeStart(WINDOWS_TOP,""); 
        Trade.INSTANCE.TradeStart(TIME_RESET,""); 

        asyncPrint("Waiting for initialization (10 seconds)...");
        try {
            Thread.sleep(10000); 
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            asyncPrint("Initialization sleep interrupted.");
            if (TRACE > 1) e.printStackTrace();
        }
        asyncPrint("Initialization wait complete.");

        Trade.INSTANCE.SetUpDownSecRcv(); 
        new Thread(() -> {
            while (true) {
                try {
                    InitMain(); 
                    Trade.INSTANCE.TradeStart(CHECK,""); 
                    Thread.sleep(5000); 
                    int upDownSecRcv = Trade.INSTANCE.GetUpDownSecRcv();
                    if (upDownSecRcv == 100) { 
                        if (REFRESH_CNT > 10) { 
                            Trade.INSTANCE.TradeStart(REFRESH,"");
                            REFRESH_CNT = 0;
                            asyncPrint("MTrade REFRESH called.");
                            Thread.sleep(5000); 
                        }
                        TradeFlg = 3; 
                        INIT_WAIT = 0; 
                        if (TRACE > 0) 
                            asyncPrint("State 100: Waiting for next signal. TradeFlg=3, INIT_WAIT=0.");
                    }
                    else if (upDownSecRcv == 200) { 
                        long startTime = System.currentTimeMillis();
                        long timeout = 30000; 
                        if (TRACE > 0) 
                            asyncPrint("State 200: Order placement window. Timeout: " + timeout/1000 + "s");
                        
                        boolean orderPlacedSignalReceived = false;
                        boolean isInitMainCalled = false;
                        while(System.currentTimeMillis() - startTime < timeout) {
                            Trade.INSTANCE.TradeStart(GETTIME,""); 
                            Thread.sleep(50); 
                            int timerSignal = Trade.INSTANCE.GetUpDownSecRcv(); 
                            if(timerSignal == 2 && !isInitMainCalled) {
                                InitMain(); 
                                isInitMainCalled = true;
                            }
                            if(timerSignal == 1) { 
                                INIT_WAIT = 1; 
                                TradeFlg = 0;  
                                Trade.INSTANCE.SetUpDownSecRcv(); 
                                asyncPrint("Signal 1 received from MTrade. INIT_WAIT=1, TradeFlg=0. Ready for MessageHandler to trade.");
                                orderPlacedSignalReceived = true;
                                Thread.sleep(2000);
                                break; 
                            }
                        }
                        if (!orderPlacedSignalReceived && TRACE > 0) {
                            asyncPrint("State 200 timeout: No trade signal (1) received from MTrade within " + timeout/1000 + "s.");
                            TradeFlg = 3; INIT_WAIT = 0;
                        }
                    } else{ 
                         if (TRACE > 0) asyncPrint("Unhandled GetUpDownSecRcv value: " + upDownSecRcv);
                         TradeFlg = 3; INIT_WAIT = 0;
                    }
                } catch (InterruptedException e) {                        asyncPrint("MTrade control thread interrupted.");
                        if (TRACE > 1) e.printStackTrace();
                        Thread.currentThread().interrupt(); 
                        break;
                } catch (UnsatisfiedLinkError ule) {
                    asyncPrint("Native library MTrade error: " + ule.getMessage());
                    if (TRACE > 1) ule.printStackTrace();
                    break;
                } catch (Exception e) {
                    asyncPrint("Error in MTrade control thread: " + e.getMessage());
                    if (TRACE > 1) e.printStackTrace();
                    try { Thread.sleep(5000); } catch (InterruptedException ie) { Thread.currentThread().interrupt(); break;}
                }
            }
            asyncPrint("MTrade control thread terminated.");
        }).start();
        
        MessageHandler messageHandlerInstance = new MessageHandler(); 
        Thread messageHandlerThread = new Thread(messageHandlerInstance); 
        messageHandlerThread.setName("MessageHandlerThread");
        messageHandlerThread.start();
        asyncPrint("MessageHandler thread started.");

        asyncPrint("Main thread waiting started.");
        while (true) {
            try {
                Thread.sleep(60000); 
                if (TRACE > 2) asyncPrint("Main thread still alive. Current TradeFlg: " + TradeFlg + ", INIT_WAIT: " + INIT_WAIT);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                asyncPrint("Main thread interrupted. Shutting down...");
                if (messageHandlerThread != null) messageHandlerThread.interrupt(); 
                messageProcessingService.shutdownNow(); 
                PrintService.shutdownNow();
                break;
            }
        }
        asyncPrint("Main thread terminated.");
    }

    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df_local = new DecimalFormat("#.##");
        try {
            return Double.parseDouble(df_local.format(value));
        } catch (NumberFormatException e) {
            return value; 
        }
    }
}
