
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>gettrade</groupId>
  <artifactId>gettrade</artifactId>
  <version>0.0.1-SNAPSHOT</version>

    <parent>
        <groupId>com.github.fommil.netlib</groupId>
        <artifactId>parent</artifactId>
        <version>1.1</version>
    </parent>
    
       <dependencies>
		<dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>1.18.1</version>
        </dependency>
 
		<dependency>
    	<groupId>com.squareup.okhttp3</groupId>
    	<artifactId>okhttp</artifactId>
    	<version>4.12.0</version>
		</dependency>
        <dependency>
            <!-- WORKAROUND: https://github.com/sbt/sbt/issues/861 -->
            <groupId>net.sourceforge.f2j</groupId>
            <artifactId>arpack_combined_all</artifactId>
            </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>core</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-osx-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-linux-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-linux-i686</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-win-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-win-i686</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_ref-linux-armhf</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-osx-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-linux-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-linux-i686</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-linux-armhf</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-win-x86_64</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
        <dependency>
            <groupId>${project.parent.groupId}</groupId>
            <artifactId>netlib-native_system-win-i686</artifactId>
            <version>${project.parent.version}</version>
            <classifier>natives</classifier>
        </dependency>
		<dependency>
		    <groupId>nz.ac.waikato.cms.weka.thirdparty</groupId>
		    <artifactId>bounce</artifactId>
		    <version>0.18</version>
		</dependency>
	    <dependency>
	        <groupId>nz.ac.waikato.cms.weka</groupId>
	        <artifactId>weka-stable</artifactId>
	        <version>3.8.5</version>
	    </dependency>
		<dependency>
		    <groupId>org.ta4j</groupId>
		    <artifactId>ta4j-core</artifactId>
		    <version>0.16</version>
		</dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>
        <dependency> <!-- JNA dependency -->
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>4.5.2</version>
        </dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.13.3</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20210307</version>
		</dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.7</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.12</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.18</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>
     
    <dependency>
        <groupId>org.java-websocket</groupId>
        <artifactId>Java-WebSocket</artifactId>
        <version>1.5.4</version>
    </dependency>
    <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.10.1</version>
    </dependency>
    
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>2.0.9</version>
    </dependency>
    <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.4.11</version>
    </dependency>
    
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    
    <dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-math3</artifactId>
    <version>3.6.1</version>
</dependency>

  </dependencies>
  <build>
    <sourceDirectory>src</sourceDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.0</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>
</project>