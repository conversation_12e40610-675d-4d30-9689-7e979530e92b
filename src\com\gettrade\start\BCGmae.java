package com.gettrade.start;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class BCGmae {

	public interface Trade extends Library {
		Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

		public void TradeBitqDlg_bcgame();
		public void GetBCUpDownPec();
		public void GetBCUpDownPrice();
		public void GetBCUpDownSec();
		public void GetBCBTCCheck();
		public void GetBCAmount();
		public void GetBCBusted();
		public void GetBCTCheck();
		public String GetTrade_Data();
		public void SetSW_RESTORE();
		public void TradeStart(int nflag);
		public void SetCoinSelect(long x, long y);
		public void SetBCGamePlaceOrder();
		public double GetBCUpDownPriceRec();
		public String GetEURUSD_Data();
		public double GetPnLData();
		public double GetTradeEntry();
		public void SetPnLData();
		public void SetTradeEntry();
		public void SET_1000(int x);
	}

	public static int WINDOWS_TOP = 300008;
	
	public static boolean btest = false;
	public static int trade_flag = 0;

	public static int CONFORM = 100003;
	public static int LEFTHIDE = 100013;
	public static int BUY_UP = 100004;
	public static int SELL_DOWN = 100005;
	public static int TIME_RESET = 4;
	public static int REFRESH = 304;
	public static int TRADE_STOP = 103;
	public static int REFRESH_CNT = 0;
	public static int REFRESH_CMP = 10;

	public static double volume = 0;
	public static double volumeBTC = 0;
	public static double trade_volume_max = 15;
	public static double trade_volume_min = 0;
	public static double trade_volume_spot_max = 15;
	public static int Trace = 0;
	public static double BollUp = 0.0;
	public static double BollDown = 0.0;
	public static int BollCheck = 0;
	public static int checkboll = 0;
	public static int trade_pec = 0;
	public static int change_volumn = 0;

	public static int Down_pec = 50;
	public static int Up_pec = 50;
	
	public static int TAB1 = 200;
	public static int TAB2 = 201;
	public static int TAB3 = 202;

	public static int REFRESH_SEC = 100026;
	public static int BCGAME_SEC = 100027;
	public static int GETCOINLIST = 100037;
	public static int MOVEDOWNLIST = 100038;
	public static int GETPER = 100039;
	
	// MODE 2
	
	public static int SET_1000 = 0;
	public static int SET_1000C = 0;
	public static int ACTIVE_BET = 100019;
	public static int PLACE_BET = 100020;
	public static int PUBLIC_BET = 100048;
	public static int CASH_OUT = 100021;
	public static int CASH_OUT_CONFORM = 100022;
	public static int CLOSED_BET = 100023;
	public static int CASH_OUT_CONFORM_CANCLE = 100024;
	public static int MONEY_SET = 100025;
	
	public static int CASH_SET = 100041;
	public static int CASH_SET_PROFIT_BP = 100042;
	public static int CASH_SET_PROFIT_P = 100043;
	public static int CASH_SET_LOSS_BP = 100044;
	public static int CASH_SET_LOSS_P = 100045;
	public static int CASH_SET_CONFORM = 100046;
	public static int CASH_TRADE_DATA = 100047;
	
	public static int TRADE_TYPE_VALUE = 0;

	public static int TRADE_BS = 0;
	public static int TRADE_MODE = 0;
	public static int REFRESH_FLG = 0;
	public static int STOP_TRADE = 0;
	public static double STOP_AMOUNT = 0;
	public static int START_TRADE = 0;
	public static String symbol = "BTCUSDT";

	public static double TradeCheck = 0;
	public static int RETRYCNT = 0;
	public static double orderbook_s = 1;
	public static int TRADE_COUNT = 0;
	
	// MODE VS
	public static int SET_5 = 100006;
	public static int SET_10 = 100007;
	public static int SET_15 = 100008;
	public static int SET_25 = 100009;
	public static int SET_50 = 100010;
	public static int SET_75 = 100011;
	public static int SET_100 = 100012;
	
	public static BinanceSpotApiClientFactory factory = null;
	public static BinanceApiSpotRestClient client = null;

	public static BinanceFuturesApiClientFactory BinancefactoryF = null;
	public static BinanceApiFuturesRestClient clientF = null;
		
	private static AtomicBoolean initTrade = new AtomicBoolean(false);
	private static AtomicBoolean first = new AtomicBoolean(false);
	
	public static double ASKQ = 0;
	public static double BIDQ = 0;
	public static double FASKQ = 0;
	public static double FBIDQ = 0;
    private static double BTC_PRICE1 = -1;
    private static double BTC_PRICE2 = -1;
    private static double FBTC_PRICE1 = -1;
    private static double FBTC_PRICE2 = -1;
    
	public static int TRADE_MONEY = 5;
	public static int TRADE_MONEYC = 5;
	public static double LOSS_MONEY = 1;
	public static double LOSS_CUT = 0;
	
	public static boolean ContTrade = false;
	public static Socket TradeSocket = null;
	public static PrintWriter Tradeout = null;
	private static Map<String, Integer> symbolCountResultMap = new HashMap<>();
			
	public static SimpleDateFormat timeFormat;
	public static BufferedWriter out = null;
    private static final ExecutorService PrintService = Executors.newSingleThreadExecutor();
    public static void asyncPrint(String message) {
    	PrintService.submit(() -> System.out.println(message));
    }
    	
    private static final ExecutorService messageProcessingService = Executors.newSingleThreadExecutor();
    
    static class MessageHandler implements Runnable {
        private boolean bfirstBTC20 = false;
        private boolean bfirstFBTC20 = false;
        private boolean bfirstBTC_PRICE = false;
        private boolean bfirstFBTC_PRICE = false;
        
        private String[] orderbook20MSg = new String[4];

        
        @Override
        public void run() {
            			
            while (true) {
                try (Socket socket = new Socket("*************", 22222)) {
                    BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
                    out = new BufferedWriter(new OutputStreamWriter(socket.getOutputStream()));
                    while (true) {
                    	String message = in.readLine();
                    	if (message != null) {
                    	    messageProcessingService.submit(() -> {
                                JsonObject jsonObject = JsonParser.parseString(message).getAsJsonObject();
                                if (jsonObject.has("Mtype") && jsonObject.has("message")) {    
                                    String mtype = jsonObject.get("Mtype").getAsString();
                                    if ("BTC20".equals(mtype)) {
                                        bfirstBTC20 = true;
                                        String msg = jsonObject.get("message").getAsString();
                                        orderbook20MSg = msg.split("-");
                                        BIDQ = Double.parseDouble(orderbook20MSg[0]);
                                        ASKQ = Double.parseDouble(orderbook20MSg[1]);
                                    } else if ("FBTC20".equals(mtype)) {
                                        bfirstFBTC20 = true;
                                        String msg = jsonObject.get("message").getAsString();
                                        orderbook20MSg = msg.split("-");
                                        FBIDQ = Double.parseDouble(orderbook20MSg[0]);
                                        FASKQ = Double.parseDouble(orderbook20MSg[1]);
                                    }else if ("BTC_PRICE".equals(mtype)) {
                                    	bfirstBTC_PRICE = true;
                                        String msg = jsonObject.get("message").getAsString();
                                        orderbook20MSg = msg.split("-");
                                        BTC_PRICE1 = Double.parseDouble(orderbook20MSg[0]);
                                        BTC_PRICE2 = Double.parseDouble(orderbook20MSg[1]);
                                    }else if ("FBTC_PRICE".equals(mtype)) {
                                    	bfirstFBTC_PRICE = true;
                                        String msg = jsonObject.get("message").getAsString();
                                        orderbook20MSg = msg.split("-");
                                        FBTC_PRICE1 = Double.parseDouble(orderbook20MSg[0]);
                                        FBTC_PRICE2 = Double.parseDouble(orderbook20MSg[1]);
                                    }
                                }
                                
    							if((2 > FASKQ || 2 > ASKQ) || (2 > FBIDQ || 2 > BIDQ)) {
    								try {
										out.write("3");
									} catch (IOException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
									}
    							}else {
    								try {
										out.write("4");
									} catch (IOException e) {
										// TODO Auto-generated catch block
										e.printStackTrace();
									}
    							}
    							
                    	        if (initTrade.compareAndSet(false, true)) {
            						long totalMilliseconds = System.currentTimeMillis();
            						long currentSecond = (totalMilliseconds / 1000) % 60;

            						if ((currentSecond >= 58 && currentSecond <= 59) || !first.get()) {
										InitMain();										
										initTrade.set(false);
            							first.set(true);
					                    resetTradeOrderbook();
					                    resetFlags();
            						}else {
	                    	            processMessage(message);
            						}
                    	        }
                    	    });
                    	}
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    resetTradeOrderbook();
                    resetFlags();
                    initTrade.set(false);
                    try {
                        Thread.sleep(5000); // Wait 5 seconds before attempting to reconnect
                    } catch (InterruptedException ie) {
                        ie.printStackTrace();
                    }
                }
            }
        }
        
        private void processMessage(String message) {
            if (message != null) {
                // Process the message based on its type                         
                if (bfirstBTC20 && bfirstFBTC20) {
           			if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 1 || TRADE_MODE == 3)) {
           				Random random = new Random();
           				REFRESH_CMP = random.nextInt(30) + 10;
           				Refresh_TradeMode1();
           				REFRESH_CNT = 0;

           				initTrade.set(false);
           				return;
           			} else if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 2 || TRADE_MODE == 4)) {
           				Random random = new Random();
           				REFRESH_CMP = random.nextInt(5) + 10;
           				Refresh_TradeMode2();
           				REFRESH_CNT = 0;
           				initTrade.set(false);
           				return;
           			}

           			if ((TRADE_MODE == 1 || TRADE_MODE == 3) && (!(volume > trade_volume_min && volume < trade_volume_max) || (checkboll == 1 && BollCheck == 1) || volumeBTC > trade_volume_spot_max)) {

           				if (Trace == 1)
           					asyncPrint("PASS trade_flag:" + trade_flag + " volume:" + volume + " BollCheck:" + BollCheck
           							+ " trade_volume_min:" + trade_volume_min + " trade_volume_max:" + trade_volume_max + " volumeBTC:" + volumeBTC);

           				initTrade.set(false);
           				return;
           			} else if ((TRADE_MODE == 2 || TRADE_MODE == 4) && (!(volume > trade_volume_min && volume < trade_volume_max) || (checkboll == 1 && BollCheck == 1))) {

           				if (Trace == 1)
           					asyncPrint("PASS trade_flag:" + trade_flag + " volume:" + volume + " BollCheck:" + BollCheck
           							+ " trade_volume_min:" + trade_volume_min + " trade_volume_max:" + trade_volume_max + " volumeBTC:" + volumeBTC);

           				initTrade.set(false);
           				return;
           			}

           			if (STOP_TRADE == 1 || RETRYCNT > 0) {
           				initTrade.set(false);
           				return;
           			}
            			
                    double check = 0;
                    if(volumeBTC < 10) {
                    	check = 2;
					} else if (volumeBTC > 10 && volumeBTC <= 15) {
						check = 3;
                    }else if (volumeBTC > 15 && volumeBTC <= 20) {
                    	check = 4;
                    }else if (volumeBTC > 20 && volumeBTC <= 25) {
                    	check = 5;
                    }else if (volumeBTC > 25 && volumeBTC <= 30) {
                    	check = 7;
                    }else if (volumeBTC > 30 && volumeBTC <= 40) {
                    	check = 8;
                    }else {
                    	check = 10; //(volumeBTC * 1.5) / 100;
                    }
                    
           			if (Trace == 1)
           				asyncPrint("TRADE DATE : trade_flag:" + trade_flag + " ASKQ:"+ ASKQ + " BIDQ:" + BIDQ+ " FASKQ:"+ FASKQ + " FBIDQ:" + FBIDQ+ " BTC_PRICE1:" + BTC_PRICE1 + " BTC_PRICE2:" + BTC_PRICE2 + " FBTC_PRICE1:" + FBTC_PRICE1 + " FBTC_PRICE2:" + FBTC_PRICE2+" check:"+check);
            		
           			if((TRADE_MODE == 1 || TRADE_MODE == 3) && ((Math.abs(BTC_PRICE1 - BTC_PRICE2) > check && BTC_PRICE1 > 70000 && BTC_PRICE2 > 70000) || (Math.abs(FBTC_PRICE1 - FBTC_PRICE2) > check && FBTC_PRICE1 > 70000 && FBTC_PRICE2 > 70000)) ) {
           				if ( /*TRADE_COUNT < 4  &&*/ (BTC_PRICE1 < BTC_PRICE2 || FBTC_PRICE1 < FBTC_PRICE2) && (trade_flag == 0 || trade_flag == 2) && Up_pec >= trade_pec && 2 > FASKQ && 2 > ASKQ /*&& 1 < BIDQ && 1 < FBIDQ*/) {
           					Trade.INSTANCE.TradeStart(BUY_UP);
            				System.out.println("TRADE UP");
  
           					trade_flag = 1;
           					TRADE_COUNT++;
           					if(!getPec(1)) {
           						Refresh_TradeMode1();
           						trade_flag=100;
           					}
           				} else if ( /*TRADE_COUNT < 4 &&*/ (BTC_PRICE1 > BTC_PRICE2 || FBTC_PRICE1 > FBTC_PRICE2) && (trade_flag == 0 || trade_flag == 1) && Down_pec >= trade_pec && 2 > FBIDQ && 2 > BIDQ /*&& 1 < FASKQ && 1 < ASKQ*/) {
	           				Trade.INSTANCE.TradeStart(SELL_DOWN);
	           				System.out.println("TRADE DN");        					
           					trade_flag = 2;
           					TRADE_COUNT++;
           					if(!getPec(2)) {
           						Refresh_TradeMode1();
           						trade_flag=100;
           					}
           				}
           			}

           			if(TRADE_MODE == 2 || TRADE_MODE == 4) {
           				if(symbol.equals("BTCUSDT") ) {
	           					if(TRADE_BS == 1 && (/*orderbook_s > ASKQ &&*/ orderbook_s > FASKQ) && 1 < BIDQ && 1 < FBIDQ && ASKQ < BIDQ) {
	           						try {
	           							TradeBUY( false, true);
	           							System.out.println("TRADE UP");
	           						} catch (Exception e) {
	           							// TODO Auto-generated catch block
	           							e.printStackTrace();
	           						}
	           					}else if(TRADE_BS == 2 && (/*orderbook_s > BIDQ &&*/ orderbook_s > FBIDQ) && 1 < ASKQ && 1 < FASKQ && BIDQ < ASKQ) {
	           						try {
	           							TradeSELL( false,true);
	           							System.out.println("TRADE DN");
	           						} catch (Exception e) {
	           							// TODO Auto-generated catch block
	           							e.printStackTrace();
	           						}
	           					}
           				}
           			}
                }
        		initTrade.set(false);
            }        	
        }
        
        private void resetTradeOrderbook() {
        	ASKQ = 99;
        	BIDQ = 99;
        	FASKQ = 99;
        	FBIDQ = 99;
        }

        private void resetFlags() {
            bfirstBTC20 = false;
            bfirstFBTC20 = false;
        }
    }
            
    public static String oldTradeData = "";
    static class MessageHandler1 implements Runnable {
        private double entry = 0.0;
        private double exit = 0.0;
        private double multiplier = 0.0;
        private double PL = 0.0;
        @Override
        public void run() {
            while (true) {
            	long totalMilliseconds = System.currentTimeMillis();
           		long currentSecond = (totalMilliseconds / 1000) % 60;

    			if ((ContTrade == false && trade_flag == 0 && currentSecond >= 58 && currentSecond <= 59) || !first.get()) {
    				if(initTrade.compareAndSet(false, true)) {
	      		    	messageProcessingService.submit(() -> {	      		    	
							InitMain();		
		    				first.set(true);
				    		if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 1 || TRADE_MODE == 3)) {
				    			Random random = new Random();
				    			REFRESH_CMP = random.nextInt(30) + 10;
				    			Refresh_TradeMode1();
				    			REFRESH_CNT = 0;
				    		} else if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 2 || TRADE_MODE == 4)) {
				    			Random random = new Random();
				    			REFRESH_CMP = random.nextInt(5) + 10;
				    			Refresh_TradeMode2();
				    			REFRESH_CNT = 0;
				    		}
				    		initTrade.set(false);
	      		    	});
    				}
    			}else if(STOP_TRADE == 0 ){
    				Trade.INSTANCE.TradeStart(CASH_TRADE_DATA);
                	String strData = Trade.INSTANCE.GetTrade_Data();
   
    				double[] prices = new double[2];
    				prices[0] = 0;
    				//prices[1] = 0;
    				if(!ContTrade) {
    					//prices[0] = getCheckPrice();
    					//prices[1] = getCheckPrice();
	    				//if( prices[0] == 0 ) continue;
	    				//addPrice(prices[0]);
	    				//addPrice(prices[1]);
	    				//addPriceAve(Math.abs(prices[0] - prices[1]));
    				}

    				//if(pricesL.size() < 15) continue;
    				

                	strData = strData.replaceAll("^\"|\"$", "").replace("\\", "").trim();
                	if ( strData.startsWith("{") && strData.endsWith("}")) {
                		JsonObject jsonObject = JsonParser.parseString(strData).getAsJsonObject();

        				String player = jsonObject.get("player").getAsString();
        			    String pair = jsonObject.get("pair").getAsString();

        			    if (jsonObject.has("entry") && !jsonObject.get("entry").getAsString().isEmpty()) {
        			        entry = Double.parseDouble(jsonObject.get("entry").getAsString().replace(",", ""));
        			    }

        			    if (jsonObject.has("exit") && !jsonObject.get("exit").getAsString().isEmpty()) {
        			        exit = Double.parseDouble(jsonObject.get("exit").getAsString().replace(",", ""));
        			    }

        			    String multiplierStr = jsonObject.get("multiplier").getAsString();

        			    if (!multiplierStr.isEmpty()) {
        			        multiplier = Double.parseDouble(multiplierStr.substring(1).replace(",", ""));
        			    }

        			    if (jsonObject.has("PL") && !jsonObject.get("PL").getAsString().isEmpty()) {
        			        PL = Double.parseDouble(jsonObject.get("PL").getAsString().replace(",", ""));
        			    }

						if (ContTrade && initTrade.compareAndSet(false, true) || 
								((!player.equals("Kcuquhhkoyb") && !player.equals("Bojdskuioyb") && !oldTradeData.equals(strData) && pair.equals("STONKS") && entry != 0 && exit != 0 && multiplier != 0 && PL != 0 && initTrade.compareAndSet(false, true)))) {
							if(TRADE_BS == 1) {
				      	    	messageProcessingService.submit(() -> {
				      	    		if(ContTrade) {
				      	    			Trade.INSTANCE.SET_1000(SET_1000);
					      	    		processMessage1(0, 0, 1, true, false);
					      	    		initTrade.set(false);
				      	    		}else {
					      	    		if(entry < exit && multiplier >= 200 && PL < 0 ) {
					      	    			Trade.INSTANCE.SET_1000(SET_1000);
					      	    			processMessage1(0, 0, 1, false, true);
					      	    		}else {
					      	    			initTrade.set(false);
					      	    		}
				      	    		}
				      	    	});
							}else if(TRADE_BS == 2) {
				      	    	messageProcessingService.submit(() -> {
				      	    		if(ContTrade) {
				      	    			Trade.INSTANCE.SET_1000(SET_1000);
					   	    			processMessage1(0, 0, 2, true, false);
					   	    			initTrade.set(false);
				      	    		}else {
					      	    		if(entry > exit && multiplier >= 200 && PL < 0) {
					      	    			Trade.INSTANCE.SET_1000(SET_1000);
					      	    			processMessage1(0, 0, 2, false, true);
					      	    		}else {
					      	    			initTrade.set(false);
					      	    		}
				      	    		}
				      	    	});
							}else {
    			      	    	initTrade.set(false);
    			      	    }
							asyncPrint("player : " + player + ", pair : " + pair + ", entry : " + entry + ", exit : " + exit + ", multiplier : " + multiplier + ", PL : " + PL);
			  			    oldTradeData = strData;
						}
        			}
    			}
                try {
                	Thread.sleep(10);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
            }
        }
        
        private static void processMessage1(double price1, double price2, int trade, boolean cont, boolean me) {
       		if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 1 || TRADE_MODE == 3)) {
       			Random random = new Random();
       			REFRESH_CMP = random.nextInt(30) + 10;
       			Refresh_TradeMode1();
       			REFRESH_CNT = 0;

       			initTrade.set(false);
       			return;
       		} else if (REFRESH_CNT >= REFRESH_CMP && (TRADE_MODE == 2 || TRADE_MODE == 4)) {
       			REFRESH_CMP = 10;
       			Refresh_TradeMode2();
       			REFRESH_CNT = 0;
       			initTrade.set(false);
       			return;
       		}

       		if (STOP_TRADE == 1 || RETRYCNT > 0) {
       			initTrade.set(false);
       			return;
       		}
            			            					
       		if(TRADE_MODE == 1 || TRADE_MODE == 3 ) {
       			if (price1 > price2) {
       				Trade.INSTANCE.TradeStart(BUY_UP);
       				System.out.println("TRADE UP");
       				trade_flag = 1;
       			} else if (price1 < price2) {
       				Trade.INSTANCE.TradeStart(SELL_DOWN);
       				System.out.println("TRADE DN");
       				trade_flag = 2;
       			}
       		}

       		if(TRADE_MODE == 2 || TRADE_MODE == 4) {
       			if(TRADE_BS == trade) {
	      			try {
	      				TradeBUY( cont, me);
	      				System.out.println("TRADE UP");
	      			} catch (Exception e) {
	      				// TODO Auto-generated catch block
	      				e.printStackTrace();
	      			}
	      		}else if(TRADE_BS == trade) {
	      			try {
	      				TradeSELL(cont, me);
	      				System.out.println("TRADE DN");
	      			} catch (Exception e) {
	      				// TODO Auto-generated catch block
	       				e.printStackTrace();
	      			}
	       		}
       		}
        	initTrade.set(false);      	
        }
    }
    
	static public class SymbolManager {
		private static final Map<String, Integer> symbolMap;
		private static final Map<String, String> symbolsMap;
		static {
			symbolMap = new HashMap<>();
			symbolMap.put("1000SHIB", 30);
			symbolMap.put("STONKS", 29);
			symbolMap.put("FTM", 28);
			symbolMap.put("APT", 27);
			symbolMap.put("TIA", 26);
			symbolMap.put("ARB", 25);
			symbolMap.put("JUP", 24);
			symbolMap.put("ZEC", 23);
			symbolMap.put("XRP", 22);
			symbolMap.put("BCH", 21);
			symbolMap.put("AVAX", 20);
			symbolMap.put("1000LUNC", 19);
			symbolMap.put("IO", 18);
			symbolMap.put("SEI", 17);
			symbolMap.put("LINK", 16);
			symbolMap.put("DOT", 15);
			symbolMap.put("NOT", 14);
			symbolMap.put("1000FLOKI", 13);
			symbolMap.put("JASMY", 12);
			symbolMap.put("SOL", 11);
			symbolMap.put("WIF", 10);
			symbolMap.put("MATIC", 9);
			symbolMap.put("WLD", 8);
			symbolMap.put("LTC", 7);
			symbolMap.put("1000BONK", 6);
			symbolMap.put("1000PEPE", 5);
			symbolMap.put("PEOPLE", 4);
			symbolMap.put("BOME", 3);
			symbolMap.put("DOGE", 2);
			symbolMap.put("ETH", 1);
			symbolMap.put("BTC", 0);
		}

		static {
			symbolsMap = new HashMap<>();
			symbolsMap.put("1000shibusdt", "1000SHIB");
			symbolsMap.put("stonksusdt", "STONKS");
			symbolsMap.put("ftmusdt", "FTM");
			symbolsMap.put("aptusdt", "APT");
			symbolsMap.put("tiausdt", "TIA");
			symbolsMap.put("arbusdt", "ARB");
			symbolsMap.put("jupusdt", "JUP");
			symbolsMap.put("zecusdt", "ZEC");
			symbolsMap.put("xrpusdt", "XRP");
			symbolsMap.put("bchusdt", "BCH");
			symbolsMap.put("avaxusdt", "AVAX");
			symbolsMap.put("1000luncusdt", "1000LUNC");
			symbolsMap.put("iousdt", "IO");
			symbolsMap.put("seiusdt", "SEI");
			symbolsMap.put("linkusdt", "LINK");
			symbolsMap.put("dotusdt", "DOT");
			symbolsMap.put("notusdt", "NOT");
			symbolsMap.put("1000flokiusdt", "1000FLOKI");
			symbolsMap.put("jasmyusdt", "JASMY");
			symbolsMap.put("solusdt", "SOL");
			symbolsMap.put("wifusdt", "WIF");
			symbolsMap.put("maticusdt", "MATIC");
			symbolsMap.put("wldusdt", "WLD");
			symbolsMap.put("ltcusdt", "LTC");
			symbolsMap.put("1000bonkusdt", "1000BONK");
			symbolsMap.put("1000pepeusdt", "1000PEPE");
			symbolsMap.put("peopleusdt", "PEOPLE");
			symbolsMap.put("bomeusdt", "BOME");
			symbolsMap.put("dogeusdt", "DOGE");
			symbolsMap.put("ethusdt", "ETH");
			symbolsMap.put("btcusdt", "BTC");
		}

		public static int getSymbolValue(String symbol) {
			return symbolMap.getOrDefault(symbol, -1);
		}

		public static String getSymbolsValue(String symbol) {
			return symbolsMap.getOrDefault(symbol, null);
		}

		public static String getSymbolFromValue(int value) {
			for (Map.Entry<String, Integer> entry : symbolMap.entrySet()) {
				if (entry.getValue().equals(value)) {
					return entry.getKey();
				}
			}
			return null; // Return null if no matching symbol is found
		}

		public static String getSymbolsFromValue(String value) {
			for (Map.Entry<String, String> entry : symbolsMap.entrySet()) {
				if (entry.getValue().equals(value)) {
					return entry.getKey();
				}
			}
			return null; // Return null if no matching symbol is found
		}
	}


	static class ServerRunnable implements Runnable {
	    @Override
	    public void run() {
	        try (ServerSocket serverSocket = new ServerSocket(21212)) {
	            System.out.println("Server is listening on port 21212");
	            while (true) {
	                Socket socket = serverSocket.accept();
	                System.out.println("Client connected");
	                new Thread(new ClientHandler(socket)).start();
	            }
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }
	}
	
	static class ClientHandler implements Runnable {
	    private Socket socket;

	    public ClientHandler(Socket socket) {
	        this.socket = socket;
	    }

	    @Override
	    public void run() {
	        try (BufferedReader input = new BufferedReader(new InputStreamReader(socket.getInputStream()));
	             PrintWriter output = new PrintWriter(socket.getOutputStream(), true)) {

	            String message;
	            while ((message = input.readLine()) != null) {
	            	if(message.equals("1")) {
	            		ContTrade = true;
	            	}else if(message.equals("2")) {
	            		ContTrade = false;
	            	}
	                System.out.println("Received: " + message);
	            }
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }
	}
		

	public static void connectToServer(String client, int port) {
	    while (true) {
	        try {
	            TradeSocket = new Socket(client, port);
	            Tradeout = new PrintWriter(TradeSocket.getOutputStream(), true);
	            System.out.println("Connected to server");
	
	            // Add your logic to handle communication with the server here
	
	            break; // Exit the loop if connection is successful
	        } catch (IOException e) {
	            System.err.println("Connection failed, retrying in 5 seconds...");
	            try {
	                Thread.sleep(5000); // Wait 5 seconds before retrying
	            } catch (InterruptedException ie) {
	                ie.printStackTrace();
	            }
	        }
	    }
	}
	
	static String ClientIP = "";
	public static void main(String[] args) throws InterruptedException, IOException {

		symbolCountResultMap.clear();
		
		factory = BinanceAbstractFactory.createSpotFactory("", "");
		client = factory.newRestClient();

		BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
		clientF = BinancefactoryF.newRestClient();
		
		if (!btest) {
			Trade.INSTANCE.TradeBitqDlg_bcgame();
			Trade.INSTANCE.TradeStart(TIME_RESET);
		}

		String filePathF = "c:/Trade/Trade.properties";

		int bBreak = 0;
		while(bBreak == 0) {
			try (InputStream inputF = new FileInputStream(filePathF)) {
				Properties propF = new Properties();
				propF.load(inputF);
				TRADE_MODE = Integer.parseInt(propF.getProperty("TRADE_MODE", "0"));
				change_volumn = Integer.parseInt(propF.getProperty("change_volumn", "25"));
				TRADE_TYPE_VALUE=Integer.parseInt(propF.getProperty("TRADE_TYPE_VALUE", "0"));
				ClientIP=propF.getProperty("CLIENT", "*************");
				bBreak = Integer.parseInt(propF.getProperty("INIT_WAIT", "0"));
				Thread.sleep(2000);
			} catch (IOException e) {
				// Handle the exception
				e.printStackTrace();
			}
		}
		
		Thread.sleep(20000);

		Trade.INSTANCE.TradeStart(WINDOWS_TOP);
		//Trade.INSTANCE.TradeStart(1);
		
		if(TRADE_TYPE_VALUE == 29) {
	        Thread thread = new Thread(new MessageHandler1());
	        thread.start();
	        
	        /*
	        Thread serverThread = new Thread(new ServerRunnable());
	        serverThread.start();
	        
	        Runnable connectTask = () -> connectToServer(ClientIP, 21212);
	        Thread connectThread = new Thread(connectTask);
	        connectThread.start();
	        */
	        
		}else {
			timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.KOREA);			
	        Thread thread = new Thread(new MessageHandler());
	        thread.start();
		}
		while (true) {
			try {
				Thread.sleep(2000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

	public static double getAmount() {

		File file = new File("bcamount.html");
		file.delete();

		if (!btest)
			Trade.INSTANCE.GetBCAmount();

		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		double price = 0.0;
		if (file.exists() && file.length() > 0) {
			try {
				FileInputStream fileInputStream = new FileInputStream(file);
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, "UTF-8"));
				String line = bufferedReader.readLine();
				String pattern = "\"([^\"]*)\"";
				Pattern regex = Pattern.compile(pattern);
				Matcher matcher = regex.matcher(line);

				if (matcher.find()) {
					String data = matcher.group(1);
					data = data.replaceAll("USDT", "");
					data = data.replaceAll("POL", "");
					data = data.replaceAll(" ", "");
					if (data.length() > 0) {
						price = Double.parseDouble(data);
						System.out.println("getAmount :" + price);
					}else {
						System.out.println("getAmount :" + data);
					}
				}
				bufferedReader.close();
			} catch (IOException e) {
				e.printStackTrace();
				price = 0;
			}

			file.delete();
		}

		return price;
	}
	
	public static String[] getSelectCoinList() {
		File file = new File("selectCoinList.html");
		file.delete();
		if (!btest)
			Trade.INSTANCE.TradeStart(GETCOINLIST);

		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		String[] result = null;
		if (file.exists() && file.length() > 0) {
			try {
				FileInputStream fileInputStream = new FileInputStream(file);
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, "UTF-8"));
				String line = bufferedReader.readLine();
				String pattern = "\"([^\"]*)\"";
				Pattern regex = Pattern.compile(pattern);
				Matcher matcher = regex.matcher(line);
				if (matcher.find()) {
					String data = matcher.group(1);
					data = data.replaceAll(" ", "");
					System.out.println("GETCOINLIST ENTRY :" + data);
					if (data.length() > 0) {
						result = data.split(",");
					}
				}
				bufferedReader.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			file.delete();
		}
				
		return result;
	}

	public static int getCheckBTC() {

		long startTime = System.nanoTime();

		File file = new File("bcbtccheck.html");
		file.delete();

		while (true) {
			if (!btest)
				Trade.INSTANCE.GetBCBTCCheck();

			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

			if (file.exists() && file.length() > 0) {
				try {
					FileInputStream fileInputStream = new FileInputStream(file);
					BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, "UTF-8"));
					String line = bufferedReader.readLine();
					String pattern = "\"([^\"]*)\"";
					Pattern regex = Pattern.compile(pattern);
					Matcher matcher = regex.matcher(line);

					if (matcher.find()) {
						String data = matcher.group(1);
						data = data.replaceAll(" ", "");
						System.out.println("CHECK BTC :" + data);

						if (data.length() > 0) {
							if (data.equals("1000SHIB")) {
								bufferedReader.close();
								file.delete();
								return 30;
							} else if (data.equals("STONKS")) {
								bufferedReader.close();
								file.delete();
								return 29;
							} else if (data.equals("FTM")) {
								bufferedReader.close();
								file.delete();
								return 28;
							} else if (data.equals("APT")) {
								bufferedReader.close();
								file.delete();
								return 27;
							} else if (data.equals("TIA")) {
								bufferedReader.close();
								file.delete();
								return 26;
							} else if (data.equals("ARB")) {
								bufferedReader.close();
								file.delete();
								return 25;
							} else if (data.equals("JUP")) {
								bufferedReader.close();
								file.delete();
								return 24;
							} else if (data.equals("ZEC")) {
								bufferedReader.close();
								file.delete();
								return 23;
							} else if (data.equals("XRP")) {
								bufferedReader.close();
								file.delete();
								return 22;
							} else if (data.equals("BCH")) {
								bufferedReader.close();
								file.delete();
								return 21;
							} else if (data.equals("AVAX")) {
								bufferedReader.close();
								file.delete();
								return 20;
							} else if (data.equals("1000LUNC")) {
								bufferedReader.close();
								file.delete();
								return 19;
							} else if (data.equals("IO")) {
								bufferedReader.close();
								file.delete();
								return 18;
							} else if (data.equals("SEI")) {
								bufferedReader.close();
								file.delete();
								return 17;
							} else if (data.equals("LINK")) {
								bufferedReader.close();
								file.delete();
								return 16;
							} else if (data.equals("DOT")) {
								bufferedReader.close();
								file.delete();
								return 15;
							} else if (data.equals("NOT")) {
								bufferedReader.close();
								file.delete();
								return 14;
							} else if (data.equals("1000FLOKI")) {
								bufferedReader.close();
								file.delete();
								return 13;
							} else if (data.equals("JASMY")) {
								bufferedReader.close();
								file.delete();
								return 12;
							} else if (data.equals("SOL")) {
								bufferedReader.close();
								file.delete();
								return 11;
							} else if (data.equals("WIF")) {
								bufferedReader.close();
								file.delete();
								return 10;
							} else if (data.equals("MATIC")) {
								bufferedReader.close();
								file.delete();
								return 9;
							} else if (data.equals("WLD")) {
								bufferedReader.close();
								file.delete();
								return 8;
							} else if (data.equals("LTC")) {
								bufferedReader.close();
								file.delete();
								return 7;
							} else if (data.equals("1000BONK")) {
								bufferedReader.close();
								file.delete();
								return 6;
							} else if (data.equals("1000PEPE")) {
								bufferedReader.close();
								file.delete();
								return 5;
							} else if (data.equals("PEOPLE")) {
								bufferedReader.close();
								file.delete();
								return 4;
							} else if (data.equals("BOME")) {
								bufferedReader.close();
								file.delete();
								return 3;
							} else if (data.equals("DOGE")) {
								bufferedReader.close();
								file.delete();
								return 2;
							} else if (data.equals("ETH")) {
								bufferedReader.close();
								file.delete();
								return 1;
							} else if (data.equals("BTC")) {
								bufferedReader.close();
								file.delete();
								return 0;
							} else {
								System.out.println("COIN TYPE ERROR:" + data);
								bufferedReader.close();
								file.delete();
								return -1;
							}
						}
					}
					bufferedReader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}

				file.delete();
			}
			long endTime = System.nanoTime();
			long elapsedTime = endTime - startTime;
			double seconds = (double) elapsedTime / 1_000_000_000.0;
			if (seconds > 5)
				break;
		}

		return -1;
	}

	public static double getCheckTrade() {

		double trade = -1;
		
		Trade.INSTANCE.GetBCTCheck();
		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		trade = Trade.INSTANCE.GetTradeEntry();
		asyncPrint("CHECK_TRADE ENTRY:" + trade);
		if (trade == 0) {
			trade = -1;
		}
		return trade;
		
	}
	
	public static double getCheckPnl() {

		double pnl = -1;
		
		Trade.INSTANCE.GetBCBusted();
		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		pnl = Trade.INSTANCE.GetPnLData();
		asyncPrint("CHECK_TRADE P&L:" + pnl);

		return pnl;
		
	}

	public static void getCheckPec() {

		File file = new File("c:\\trade\\bcupdownpec.html");
		file.delete();

		if (!btest)
			Trade.INSTANCE.GetBCUpDownPec();

		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (file.exists() && file.length() > 0) {
			try {
				FileInputStream fileInputStream = new FileInputStream(file);
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, "UTF-8"));
				String line = bufferedReader.readLine();
				String pattern = "\"([^\"]*)\"";
				Pattern regex = Pattern.compile(pattern);
				Matcher matcher = regex.matcher(line);

				if (matcher.find()) {
					String data = matcher.group(1);
					if (data.length() > 0) {
						Down_pec = Integer.parseInt(data.substring(0, 2));
						Up_pec = Integer.parseInt(data.substring(5, 7));
						System.out.println("GET PEC Down_pec:" + Down_pec + " ,UP:" + Up_pec);
					}
				}
				bufferedReader.close();
			} catch (IOException e) {
				e.printStackTrace();
			}

			file.delete();
		}
	}

	public static boolean getPec(int trade) {

		File file = new File("bcpec.html");
		file.delete();

		try {
			Thread.sleep(2000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		if (!btest)
			Trade.INSTANCE.TradeStart(GETPER);

		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (file.exists() && file.length() > 0) {
			try {
				FileInputStream fileInputStream = new FileInputStream(file);
				BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream, "UTF-8"));
				String line = bufferedReader.readLine();
				String pattern = "\"([^\"]*)\"";
				Pattern regex = Pattern.compile(pattern);
				Matcher matcher = regex.matcher(line);

				if (matcher.find()) {
					String data = matcher.group(1);
					data = data.replaceAll(" ", "");
					data = data.replaceAll(",", "");
					data = data.replaceAll("%", "");
					data = data.replaceAll(".", "");
					if (data.length() > 0) {
						int pec = Integer.parseInt(data.substring(0, 2));
						System.out.println("CHECK_TRADE PEC:" + pec);
						if(pec < 50) {
							bufferedReader.close();
							file.delete();
							return false;
						}
						if(trade == 1) Up_pec = pec;
						else           Down_pec = pec;
					}
				}
				bufferedReader.close();
			} catch (IOException e) {
				e.printStackTrace();
			}

			file.delete();
		}
		
		return true;
	}
		
	public static double getCheckPrice() {

		long startTime = System.nanoTime();
		while (true) {
		    Trade.INSTANCE.GetBCUpDownPrice();
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		    double price = Trade.INSTANCE.GetBCUpDownPriceRec();
		    if(price > 0) {
		    	return price;
		    }
		    
			long endTime = System.nanoTime();
			long elapsedTime = endTime - startTime;
			double seconds = (double) elapsedTime / 1_000_000_000.0;
			if (seconds > 2)
				return 0;
		}
	}
	
	public static void Refresh_TradeMode1() {

		try {
			System.out.println("======== (REFRESH1) ========");
			Trade.INSTANCE.TradeStart(REFRESH);
			Thread.sleep(7000);

			while (true) {
				double check = getCheckPrice();
				if (check > 0) {
					System.out.println("Refresh_TradeMode1 break");
					break;
				}else {
					Trade.INSTANCE.TradeStart(BCGAME_SEC);
					Trade.INSTANCE.TradeStart(REFRESH_SEC);
				}
			}
			
			Thread.sleep(3000);

			Trade.INSTANCE.TradeStart(REFRESH_SEC);
			Trade.INSTANCE.SetBCGamePlaceOrder();
							
			Trade.INSTANCE.TradeStart(LEFTHIDE);
			
			//Thread.sleep(2000);
			
			//getSelectCoinList();
			
			try {
				Thread.sleep(2000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
						
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	public static void Refresh_TradeMode2() {
		try {
			System.out.println("======== (REFRESH2) ========");
			if(TRADE_TYPE_VALUE != 29) {
				Trade.INSTANCE.TradeStart(TAB2);
				Thread.sleep(500);
				Trade.INSTANCE.TradeStart(REFRESH);
				Thread.sleep(500);
				Trade.INSTANCE.TradeStart(TAB1);
				Thread.sleep(500);
			}
			Trade.INSTANCE.TradeStart(REFRESH);
			Thread.sleep(8000);
			while (true) {
				double check = getCheckPrice();
				Trade.INSTANCE.TradeStart(BCGAME_SEC);
				Thread.sleep(500);
				Trade.INSTANCE.TradeStart(REFRESH_SEC);
				if (check > 0)
					break;
			}
			if(TRADE_TYPE_VALUE != 29) {
				Trade.INSTANCE.TradeStart(TAB2);
				Thread.sleep(1500);
				Trade.INSTANCE.TradeStart(TAB1);
				Thread.sleep(1500);
			}
			Trade.INSTANCE.TradeStart(LEFTHIDE);
			Thread.sleep(1000);
			Trade.INSTANCE.TradeStart(ACTIVE_BET);
			Thread.sleep(100);
			Trade.INSTANCE.TradeStart(MONEY_SET);
			Thread.sleep(100);
			Chang_UpDown();
			Trade.INSTANCE.TradeStart(PUBLIC_BET);
			Thread.sleep(1000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}

	private static void TradeBUY(boolean cont, boolean me) throws Exception {

		long totalMilliseconds = System.currentTimeMillis();
		long currentSecond = (totalMilliseconds / 1000) % 60;
		
		long startTime = 0;
		long endTime = 0;
		long elapsedTime = 0;
		double seconds = 0;
		boolean refresh = false;
		if (!btest) {
			Trade.INSTANCE.TradeStart(PLACE_BET);
			Trade.INSTANCE.TradeStart(ACTIVE_BET);
			startTime = System.nanoTime();
			while (true) {
				double checkTR = getCheckTrade();
				if (checkTR == TRADE_MONEY) {
					break;
				}else if(checkTR > 0) {
					Trade.INSTANCE.TradeStart(MONEY_SET);
					break;
				}
				endTime = System.nanoTime();
				elapsedTime = endTime - startTime;
				seconds = (double) elapsedTime / 1_000_000_000.0;
				if(seconds > 2.5) {
					Trade.INSTANCE.TradeStart(CLOSED_BET);
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(ACTIVE_BET);
					Thread.sleep(1000);
					refresh = true;
					break;
				}
            }

			if(TRADE_TYPE_VALUE == 29) {
				boolean bloseFlg = false;
				double oldpnl = 0;
				int pnlcnt = 0;
				while (true) {
					double pnl = 0;
					if(!cont) {
						pnl = getCheckPnl();
						if (oldpnl == pnl) {
							pnlcnt++;
						}else {
							pnlcnt = 0;
						}
					}
					if( pnl > 0 || pnlcnt > 10 || cont || pnl < -LOSS_CUT) {
						Trade.INSTANCE.TradeStart(CASH_OUT);
						Thread.sleep(500);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						Thread.sleep(200);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						try {
							//Tradeout.println("2");
						} catch (Exception e) {
							e.printStackTrace();
						}
						System.out.println("CASH_OUT BREAK");
						break;
					}else if (!bloseFlg && pnl < -LOSS_MONEY) {
						try {
							//Tradeout.println("1");
						} catch (Exception e) {
							e.printStackTrace();
						}
						bloseFlg = true;
					}
					oldpnl = pnl;
					try {
						if(pnlcnt > 10) {
							//Tradeout.println("2");
							break;
						}
					} catch (Exception e) {
						e.printStackTrace();
					}		
				}

				if (!refresh) {
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(CLOSED_BET);
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(ACTIVE_BET);
				}else {
					Thread.sleep(1000);
					Refresh_TradeMode2();
				}

			}else {
				Trade.INSTANCE.TradeStart(CASH_SET);
				Thread.sleep(1500);
				Trade.INSTANCE.TradeStart(CASH_SET_LOSS_P);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CASH_SET_PROFIT_P);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CASH_SET_CONFORM);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CONFORM);
	
				while (true) {
					double pnl = getCheckPnl();
					if( pnl < -2.5 || pnl > 0 || pnl == -1) {
						Thread.sleep(2000);
						System.out.println("CASH_OUT BREAK");
						if (getCheckTrade() > 0) {
							Trade.INSTANCE.TradeStart(CASH_OUT);
							Thread.sleep(500);
							Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
							Thread.sleep(200);
							Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						}
						break;
					}
				}
				Chang_UpDown();
			}
		}	

		Trade.INSTANCE.SetPnLData();
		Trade.INSTANCE.SetTradeEntry();
		Trade.INSTANCE.TradeStart(PUBLIC_BET);
		
		if(TRADE_TYPE_VALUE != 29) {
			endTime = System.nanoTime();
			elapsedTime = endTime - startTime;
			seconds = (double) elapsedTime / 1_000_000_000.0;
			if (60 - currentSecond < seconds && TRADE_TYPE_VALUE != 29) {
				System.out.println("Chang_UpDown INIT: " + (60 - currentSecond) + " ," + seconds);
				Chang_UpDown();
				Thread.sleep(2000);
			}
		}
	}

	private static void TradeSELL(boolean cont, boolean me) throws Exception {
		
		long totalMilliseconds = System.currentTimeMillis();
		long currentSecond = (totalMilliseconds / 1000) % 60;
		
		long startTime = 0;
		long endTime = 0;
		long elapsedTime = 0;
		double seconds = 0;
		boolean refresh = false;
		if (!btest) {
			Trade.INSTANCE.TradeStart(PLACE_BET);
			Trade.INSTANCE.TradeStart(ACTIVE_BET);
			startTime = System.nanoTime();
			while (true) {
				double checkTR = getCheckTrade();
				if (checkTR == TRADE_MONEY) {
					break;
				}else if(checkTR > 0) {
					Trade.INSTANCE.TradeStart(MONEY_SET);
					break;
				}
				
				endTime = System.nanoTime();
				elapsedTime = endTime - startTime;
				seconds = (double) elapsedTime / 1_000_000_000.0;
				if(seconds > 2.5) {
					Trade.INSTANCE.TradeStart(CLOSED_BET);
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(ACTIVE_BET);
					Thread.sleep(1000);
					refresh = true;
					break;
				}
            }
			if(TRADE_TYPE_VALUE == 29) {
				boolean bloseFlg = false;
				double oldpnl = 0;
				int pnlcnt = 0;
				while (true) {
					double pnl = 0;
					if(!cont) {
						pnl = getCheckPnl();
						if (oldpnl == pnl) {
							pnlcnt++;
						}else {
							pnlcnt = 0;
						}
					}
					
					if( pnl > 0 || pnlcnt > 10 || cont || pnl < -LOSS_CUT) {
						Trade.INSTANCE.TradeStart(CASH_OUT);
						Thread.sleep(500);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						Thread.sleep(200);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						try {
							//Tradeout.println("2");
						} catch (Exception e) {
							e.printStackTrace();
						}
						System.out.println("CASH_OUT BREAK");
						break;
						 
					} else if (!bloseFlg && pnl < -LOSS_MONEY) {
						try {
							//Tradeout.println("1");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
						bloseFlg = true;
					}
					oldpnl = pnl;
					try {
						if(pnlcnt > 10) {
							//Tradeout.println("2");
							break;
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
				}

				if (!refresh) {
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(CLOSED_BET);
					Thread.sleep(1000);
					Trade.INSTANCE.TradeStart(ACTIVE_BET);
				}else {
					Thread.sleep(1000);
					Refresh_TradeMode2();
				}


			}else {
				Trade.INSTANCE.TradeStart(CASH_SET);
				Thread.sleep(1500);
				Trade.INSTANCE.TradeStart(CASH_SET_LOSS_P);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CASH_SET_PROFIT_P);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CASH_SET_CONFORM);
				Thread.sleep(1000);
				Trade.INSTANCE.TradeStart(CONFORM);
	
				while (true) {
					double pnl = getCheckPnl();
					if( pnl < -2.5 || pnl > 0.1 || pnl == -1) {
						System.out.println("CASH_OUT BREAK");
						Thread.sleep(2000);
						if (getCheckTrade() > 0) {
							Trade.INSTANCE.TradeStart(CASH_OUT);
							Thread.sleep(500);
							Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
							Thread.sleep(200);
							Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						}
						break;
					}
				}
				
				Chang_UpDown();
			}

		}
		
		Trade.INSTANCE.SetPnLData();
		Trade.INSTANCE.SetTradeEntry();
		Trade.INSTANCE.TradeStart(PUBLIC_BET);
		
		if(TRADE_TYPE_VALUE != 29) {
			endTime = System.nanoTime();
			elapsedTime = endTime - startTime;
			seconds = (double) elapsedTime / 1_000_000_000.0;
			if (60 - currentSecond < seconds && TRADE_TYPE_VALUE != 29) {
				System.out.println("Chang_UpDown INIT: " + (60 - currentSecond) + " ," + seconds);
				Chang_UpDown();
				Thread.sleep(2000);
			}
		}
	}

	private static void InitMain() {
		STOP_TRADE = 0;
		REFRESH_CNT++;
		Trade.INSTANCE.TradeStart(TIME_RESET);

		try {
		   	volume = 0;
		   	List<Candlestick> candlestickList = clientF.getCandlestickBars("BTCUSDT", CandlestickInterval.ONE_MINUTE, 20, null, null);		    		   		        
		     
		   	Candlestick candlestick = null;
		    List<Double> priceList = new ArrayList<>();
			for (int i = 0; i < candlestickList.size(); i++) {
			   	candlestick = candlestickList.get(i);
    		    if( i >= 9 && i < candlestickList.size()-1) 
			    	volume += Double.valueOf(candlestick.getVolume()).doubleValue();
 
				priceList.add(Double.parseDouble(candlestick.getClose()));
			}		
			volume = volume / 10;
			
			volumeBTC = 0;
		   	candlestickList = client.getCandlestickBars("BTCUSDT", CandlestickInterval.ONE_MINUTE, 20, null, null);		    		   		        
			for (int i = 0; i < candlestickList.size(); i++) {
				candlestick = candlestickList.get(i);
    		    if( i >= 9 && i < candlestickList.size()-1) 
    		    	volumeBTC += Double.valueOf(candlestick.getVolume()).doubleValue();
 
			}	
			
			volumeBTC = volumeBTC / 10;
			BollingerBands boll = new BollingerBands(priceList, 20, 2.0);
			BollDown = boll.getdownBollingerBands();
			BollUp = boll.getUpBollingerBands();

	        LocalTime now = LocalTime.now();
	        int hour = now.getHour();
	        
			if (TRADE_MODE <= 2) {
				if ((volume > change_volumn && (TRADE_MODE == 1 || TRADE_MODE == 0)) || (hour >= 5 && hour < 9)) {
					Trade.INSTANCE.TradeStart(TAB2);
					TRADE_MODE = 2;
					CONFORM = 100003;
					LEFTHIDE = 100013;
					BUY_UP = 100016;
					SELL_DOWN = 100015;
					ACTIVE_BET = 100019;
					PLACE_BET = 100020;
					CASH_OUT = 100021;
					CASH_OUT_CONFORM = 100022;
					CLOSED_BET = 100023;
					CASH_OUT_CONFORM_CANCLE = 100024;
					MONEY_SET = 100025;
					REFRESH_FLG = 1;
					STOP_TRADE = 1;
				} else if (volume < change_volumn && (TRADE_MODE == 2 || TRADE_MODE == 0)) {
					Trade.INSTANCE.TradeStart(TAB1);
					TRADE_MODE = 1;
					REFRESH_FLG = 1;
					STOP_TRADE = 1;
				}
			} else if (TRADE_MODE == 4) {
				CONFORM = 100003;
				LEFTHIDE = 100013;
				BUY_UP = 100016;
				SELL_DOWN = 100015;
				ACTIVE_BET = 100019;
				PLACE_BET = 100020;
				CASH_OUT = 100021;
				CASH_OUT_CONFORM = 100022;
				CLOSED_BET = 100023;
				CASH_OUT_CONFORM_CANCLE = 100024;
				MONEY_SET = 100025;
			}

			if (TRADE_MODE == 1 || TRADE_MODE == 3) {
				getCheckPec();
				String filePathC = "c:/Trade/Trade.properties";
				try (InputStream inputC = new FileInputStream(filePathC)) {
					Properties propC = new Properties();
					propC.load(inputC);
					change_volumn = Integer.parseInt(propC.getProperty("change_volumn", "25"));
					trade_pec = Integer.parseInt(propC.getProperty("trade_pec", "60"));
				} catch (IOException e) {
					// Handle the exception
					e.printStackTrace();
				}

				TRADE_MONEY = 5;
				try (InputStream input = new FileInputStream(filePathC)) {
					Properties prop = new Properties();
					prop.load(input);
					trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "15"));
					trade_volume_min = Integer.parseInt(prop.getProperty("trade_volume_min", "0"));
					trade_volume_spot_max = Integer.parseInt(prop.getProperty("trade_volume_spot_max", "15"));
					Trace = Integer.parseInt(prop.getProperty("Trace", "0"));
					checkboll = Integer.parseInt(prop.getProperty("checkboll", "1"));
					trade_pec = Integer.parseInt(prop.getProperty("trade_pec", "60"));
					TRADE_MONEY = Integer.parseInt(prop.getProperty("TRADE_MONEY", "50"));
					STOP_AMOUNT = Double.parseDouble(prop.getProperty("STOP_AMOUNT", "10000"));
					TRADE_TYPE_VALUE = Integer.parseInt(prop.getProperty("TRADE_TYPE_VALUE", "0"));
				} catch (IOException e) {
					// Handle the exception
					e.printStackTrace();
				}

				System.out.println("trade_volume_max:" + trade_volume_max);
				System.out.println("trade_volume_min:" + trade_volume_min);
				System.out.println("trade_volume_spot_max:" + trade_volume_spot_max);
				System.out.println("Trace:" + Trace);
				System.out.println("BollUp:" + BollUp);
				System.out.println("BollDown:" + BollDown);
				System.out.println("volume:" + volume);
				System.out.println("volumeBTC:" + volumeBTC);
				System.out.println("Down_pec:" + Down_pec);
				System.out.println("Up_pec:" + Up_pec);
				System.out.println("checkboll:" + checkboll);
				System.out.println("trade_pec:" + trade_pec);
				System.out.println("TRADE_MONEY:" + TRADE_MONEY);
				System.out.println("TRADE_TYPE_VALUE:" + TRADE_TYPE_VALUE);
				
				BUY_UP = 100004;
				SELL_DOWN = 100005;
				MONEY_SET = 100028;
				Trade.INSTANCE.TradeStart(REFRESH_SEC);
				Trade.INSTANCE.SetBCGamePlaceOrder();

			} else {
				String filePath = "c:/Trade/Trade_1000.properties";
				InputStream input = new FileInputStream(filePath);
				Properties prop = new Properties();
				prop.load(input);

				trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max", "60"));
				trade_volume_min = Integer.parseInt(prop.getProperty("trade_volume_min", "0"));
				Trace = Integer.parseInt(prop.getProperty("Trace", "0"));
				checkboll = Integer.parseInt(prop.getProperty("checkboll", "1"));
				TRADE_MONEY = Integer.parseInt(prop.getProperty("TRADE_MONEY", "5"));
				TRADE_MONEYC = Integer.parseInt(prop.getProperty("TRADE_MONEYC", "5"));
				LOSS_MONEY = Double.parseDouble(prop.getProperty("LOSS_MONEY", "0.1"));
				STOP_AMOUNT = Double.parseDouble(prop.getProperty("STOP_AMOUNT", "10000"));
				TRADE_TYPE_VALUE = Integer.parseInt(prop.getProperty("TRADE_TYPE_VALUE", "0"));
				SET_1000 = Integer.parseInt(prop.getProperty("SET_1000", "1155"));
				SET_1000C = Integer.parseInt(prop.getProperty("SET_1000C", "1155"));
				LOSS_CUT = Double.parseDouble(prop.getProperty("LOSS_CUT", "0"));
				if(!first.get())
					START_TRADE = Integer.parseInt(prop.getProperty("START_TRADE", "0"));
				orderbook_s = Double.parseDouble(prop.getProperty("orderbook_s", "0"));
				input.close();
				System.out.println("trade_volume_max:" + trade_volume_max);
				System.out.println("trade_volume_min:" + trade_volume_min);
				System.out.println("Trace:" + Trace);
				System.out.println("BollUp:" + BollUp);
				System.out.println("BollDown:" + BollDown);
				System.out.println("volume:" + volume);
				System.out.println("checkboll:" + checkboll);
				System.out.println("TRADE_MONEY:" + TRADE_MONEY);
				System.out.println("TRADE_MONEYC:" + TRADE_MONEYC);
				System.out.println("LOSS_MONEY:" + LOSS_MONEY);
				System.out.println("TRADE_TYPE_VALUE:" + TRADE_TYPE_VALUE);
				System.out.println("START_TRADE:" + START_TRADE);
				System.out.println("orderbook_s:" + orderbook_s);
				System.out.println("SET_1000:" + SET_1000);
				System.out.println("SET_1000C:" + SET_1000C);
				System.out.println("LOSS_CUT:" + LOSS_CUT);
				
				//prof.unlock();
				Trade.INSTANCE.TradeStart(REFRESH_SEC);
			}

			double amount = getAmount();
			if (amount < STOP_AMOUNT) {
				Thread.sleep(1000);
				amount = getAmount();
				if (amount < STOP_AMOUNT) {
					STOP_TRADE = 1;
					System.out.println("STOP_AMOUNT ERROR (TRADE STOP)");
				}
			}

			// only TRADE_MODE 1,2
			if (REFRESH_FLG == 1 && TRADE_MODE == 2) {
				Refresh_TradeMode2();
				REFRESH_FLG = 0;
				REFRESH_CNT = 0;
			} else if (REFRESH_FLG == 1 && (TRADE_MODE == 1 || TRADE_MODE == 3)) {
				Refresh_TradeMode1();
				REFRESH_FLG = 0;
				REFRESH_CNT = 0;
			}

			Trade.INSTANCE.TradeStart(LEFTHIDE);

			double check = getCheckPrice();
			Thread.sleep(1000);
			if (check == getCheckPrice() || check == 0.0) {
				System.out.println("======== STOP VIEW (REFRESH)========");
				if (TRADE_MODE == 1 || TRADE_MODE == 3)
					Refresh_TradeMode1();
				if (TRADE_MODE == 2 || TRADE_MODE == 4)
					Refresh_TradeMode2();
				REFRESH_CNT = 0;
			}
				
			/*
			int checkBTC = getCheckBTC();
								
			if (checkBTC == 0 && TRADE_TYPE_VALUE == 0 ) {
				symbol = "BTCUSDT";
			}else if( checkBTC == 29 && TRADE_TYPE_VALUE == 29) {
				symbol = "STONKS";
			} else {
				System.out.println("======== symbol ERROR TYPE:" + checkBTC);
				STOP_TRADE = 1;
			}
			System.out.println("DEFAULT SYMBOL TYPE:" + symbol);
			*/
			
			if (TRADE_MODE == 2 || TRADE_MODE == 4) {
				Trade.INSTANCE.SET_1000(SET_1000);
				Thread.sleep(200);
				Chang_UpDown();
				int count = 0;
				Thread.sleep(500);
				Trade.INSTANCE.TradeStart(CLOSED_BET);
				Thread.sleep(1000);
				while (true) {
					Trade.INSTANCE.TradeStart(ACTIVE_BET);
					System.out.println("CHANGE ACTIVE_BET");
					Thread.sleep(1000);
					if (getCheckTrade() > 0) {
						Thread.sleep(300);
						Trade.INSTANCE.TradeStart(CASH_OUT);
						Thread.sleep(300);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						Thread.sleep(200);
						Trade.INSTANCE.TradeStart(CASH_OUT_CONFORM);
						System.out.println("CASH_OUT");
						Thread.sleep(1000);
						try {
							Tradeout.println("2");
						} catch (Exception e) {
							e.printStackTrace();
						}

						count++;
						if (count > 4)
							break;
					} else {
						// System.out.println("ELSE BREAK");
						break;
					}
				}
				Trade.INSTANCE.TradeStart(MONEY_SET);
				Thread.sleep(200);
				Trade.INSTANCE.TradeStart(PUBLIC_BET);
			} else if (TRADE_MODE == 1 || TRADE_MODE == 3) {
				Trade.INSTANCE.TradeStart(MONEY_SET);
				System.out.println("MONEY_SET data: " + TRADE_MONEY);
				//if(!getPlaceOrder())
				//	Trade.INSTANCE.TradeStart(PlaceOrder);
			}
			
			System.out.println("STOP_AMOUNT:" + STOP_AMOUNT);
			System.out.println("TRADE_MODE:" + TRADE_MODE);
			System.out.println("REFRESH_CNT:" + REFRESH_CNT);
			System.out.println("REFRESH_CMP:" + REFRESH_CMP);
			System.out.println("symbol:" + symbol);

			if (Double.parseDouble(candlestick.getHigh()) > BollUp
					|| Double.parseDouble(candlestick.getLow()) < BollDown)
				BollCheck = 1;
			else
				BollCheck = 0;

		} catch (Exception e) {
			System.out.println("Failed to get candlestick data: " + e.getMessage());
			volume = 0;
			STOP_TRADE = 1;
		}
		
		Trade.INSTANCE.TradeStart(MONEY_SET);
		trade_flag = 0;
		TRADE_COUNT = 0;
		
		/*
		try {
			Trade.INSTANCE.TradeStart(TAB2);
			Thread.sleep(500);
			Trade.INSTANCE.TradeStart(TAB1);
			Thread.sleep(500);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		*/
		System.out.println("STOP_TRADE:" + STOP_TRADE);
	}

	public static int getSymbolIndex(String[] symbolList, String targetSymbol) {
		for (int i = 0; i < symbolList.length; i++) {
			if (symbolList[i].equals(targetSymbol)) {
				return i;
			}
		}
		return -1; // Return -1 if the symbol is not found in the list
	}

	public static String getSymbolFromIndex(String[] symbolList, int index) {
		if (index >= 0 && index < symbolList.length) {
			return symbolList[index];
		}
		return null; // Return null if the index is out of range
	}
	
	public static void Chang_UpDown() {
		if(TRADE_TYPE_VALUE == 29) {
			//Random random = new Random();
			//int randomNumber = random.nextInt(2); 
			//START_TRADE = randomNumber;
	        if(START_TRADE == 1) {
				Trade.INSTANCE.TradeStart(BUY_UP);
				TRADE_BS = 1;
				System.out.println("============= BUY CHANGE ==========");
	        }else {
				Trade.INSTANCE.TradeStart(SELL_DOWN);
				TRADE_BS = 2;
				System.out.println("============= SELL CHANGE ==========");
	        }
		}
	}
	
}