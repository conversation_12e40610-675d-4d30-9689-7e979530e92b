package com.gettrade.start;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.binance.api.client.api.sync.BinanceApiFuturesRestClient;
import com.binance.api.client.api.sync.BinanceApiSpotRestClient;
import com.binance.api.client.domain.account.TradeHistoryItem;
import com.binance.api.client.domain.market.Candlestick;
import com.binance.api.client.domain.market.CandlestickInterval;
import com.binance.api.client.domain.market.OrderBook;
import com.binance.api.client.domain.market.OrderBookEntry;
import com.binance.api.client.exception.BinanceApiException;
import com.binance.api.client.factory.BinanceAbstractFactory;
import com.binance.api.client.factory.BinanceFuturesApiClientFactory;
import com.binance.api.client.factory.BinanceSpotApiClientFactory;
import com.sun.jna.Library;
import com.sun.jna.Native;

public class TheOption {

	public interface Trade extends Library {
		Trade INSTANCE = (Trade) Native.loadLibrary("MTrade", Trade.class);

		public void TradeBitqDlg();
		public void TradeStart(int nflag);
	}
	
	public static boolean btest=false;
	public static boolean init = false;
	public static int trade_flag = 0;
	public static boolean AveCandleFlg = false;
	public static double AveValue = 0;
	
	public static int BUY_UP = 200001;
	public static int SELL_DOWN = 200002;
	public static int TIME_RESET = 4;
	public static int REFRESH_CMP = 10;
	public static int REFRESH_CNT = 1;
	public static int REFRESH = 303;
	public static int TRADE_STOP = 103;	

	public static double volume = 0;
	public static double orderbook_S1 = 0.001;
	public static double orderbook_E1 = 1.5;
	public static double orderbook_S2 = 1.5;
	public static double orderbook_E2 = 1.5;	
	public static double trade_volume_max = 15;
	public static double trade_volume_min = 0;
	
	public static int get_orderbook_coin_M = 5;
	public static int start_time = 0;
	public static int end_time = 40;
	public static int Trace = 0;
	public static double BollUp = 0.0;
	public static double BollDown = 0.0;
	public static int BollCheck = 0;
	public static int TradeOne = 0;
	public static int IsTrade = 0;
	public static int IsTradeValueMAX = 0;
	public static int IsTradeValueMIN = 0;
	public static int ContinueTrade = 0;
	
	public static BinanceSpotApiClientFactory factory = null;
	public static BinanceApiSpotRestClient client = null;
	public static BinanceFuturesApiClientFactory BinancefactoryF = null;
	public static BinanceApiFuturesRestClient clientF = null;

	public static void main(String[] args) throws InterruptedException, IOException {

		//System.setProperty("https.proxyHost", "*************");
		//System.setProperty("https.proxyPort", "8088");
		
		factory = BinanceAbstractFactory.createSpotFactory("", "");
		client = factory.newRestClient();
		
		BinancefactoryF = BinanceAbstractFactory.createFuturesFactory("", "");
		clientF = BinancefactoryF.newRestClient();
	    
		if(!btest) {
			Trade.INSTANCE.TradeBitqDlg();
			Trade.INSTANCE.TradeStart(TIME_RESET);
		}

		try {
			Thread.sleep(15000);
		} catch (InterruptedException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//Trade.INSTANCE.TradeStart(1);
		        	
		SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.KOREA);
		SimpleDateFormat checkhour = new SimpleDateFormat("HH", Locale.KOREA);
        ScheduledExecutorService serviceGetData = Executors.newSingleThreadScheduledExecutor();
        AtomicBoolean isRunning = new AtomicBoolean(false);

        Runnable runnableGetData = new Runnable() {
            @Override
            public void run() {
            	try {
            		if (isRunning.compareAndSet(false, true)) {
	            	
	    		        String symbol = "BTCUSDT";      	
		    			long totalMilliseconds = System.currentTimeMillis();
		    			long currentSecond = (totalMilliseconds/ 1000) % 60;
		    			if (!(currentSecond >= start_time && currentSecond <= end_time) )  {
		    			
		    				if(!AveCandleFlg && currentSecond == start_time-1) {
			    				AveValue = 0;
		    					AveCandleFlg = true;
		    					List<Candlestick> candlestickListF = clientF.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 10, null, null);
	    		   		    	Candlestick candlestickF = null;
	    		    			for (int i = 0; i < candlestickListF.size(); i++) {
	    		    				candlestickF = candlestickListF.get(i);
	    		    				AveValue += Math.abs(Double.valueOf(candlestickF.getOpen()).doubleValue() - Double.valueOf(candlestickF.getClose()).doubleValue());
	    		    			}
	    		    			
	    		    			AveValue = AveValue / candlestickListF.size();
	    		    			AveValue = roundToTwoDecimalPlaces(AveValue);
		    				}
		        			if(currentSecond >= 58 && currentSecond <= 59 && !init) {
		        				Trade.INSTANCE.TradeStart(TIME_RESET);
		        				init = true;
		    	    		        
		        				if(REFRESH_CNT >= REFRESH_CMP) {
		        					Trade.INSTANCE.TradeStart(REFRESH);
		        					REFRESH_CNT = 0;
		        					
			                        Random random = new Random();
			                        REFRESH_CMP = random.nextInt(11) + 6;
		        				}
		        				REFRESH_CNT++;
		        				
		    		   		    try {
		    		   		    	volume = 0;
		    		   		    	List<Candlestick> candlestickList = client.getCandlestickBars(symbol, CandlestickInterval.ONE_MINUTE, 20, null, null);		    		   		        
		    		   		     
		    		   		    	Candlestick candlestick = null;
		    		   		        List<Double> priceList = new ArrayList<>();
		    		    			for (int i = 0; i < candlestickList.size(); i++) {
		    		    			    candlestick = candlestickList.get(i);
		    		    				priceList.add(Double.parseDouble(candlestick.getClose()));
		    		    				if( i >= 15) 
		    		    					volume += Double.valueOf(candlestick.getVolume()).doubleValue();
		    		    			}		
		    		    			volume = volume / 5;
		    		    			volume = roundToTwoDecimalPlaces(volume);
		    			    			
		    		    			BollingerBands boll = new BollingerBands(priceList, 20, 2.0);
		    		    			BollDown = boll.getdownBollingerBands();
		    		    			BollUp = boll.getUpBollingerBands();
		    		    					    		    			
		    		    			String filePath = "c:/Trade/Trade_option.properties";
		    		    			InputStream input = new FileInputStream(filePath);
	
	    			   	            Properties prop = new Properties();
		    		    	        prop.load(input);
		    			    	    	
		    		    	    	orderbook_S1 = Double.parseDouble(prop.getProperty("orderbook_S1","0.001"));
		    		    	    	orderbook_E1 = Double.parseDouble(prop.getProperty("orderbook_E1","1"));
		    		    	    	orderbook_S2 = Double.parseDouble(prop.getProperty("orderbook_S2","4"));
		    		    	    	orderbook_E2 = Double.parseDouble(prop.getProperty("orderbook_E2","10"));
		    		    	        get_orderbook_coin_M = Integer.parseInt(prop.getProperty("get_orderbook_coin_M","5"));
		    		    	        trade_volume_max = Integer.parseInt(prop.getProperty("trade_volume_max","40"));
		    		    	        trade_volume_min = Integer.parseInt(prop.getProperty("trade_volume_min","20"));
		    		    	        start_time = Integer.parseInt(prop.getProperty("start_time","0"));
		    		    	        end_time = Integer.parseInt(prop.getProperty("end_time","40"));
		    		    	        Trace = Integer.parseInt(prop.getProperty("Trace","0"));
		    		    	        TradeOne = Integer.parseInt(prop.getProperty("TradeOne","0"));
		    		    	        IsTrade = Integer.parseInt(prop.getProperty("IsTrade","0"));
		    		    	        IsTradeValueMAX = Integer.parseInt(prop.getProperty("IsTradeValueMAX","0"));
		    		    	        IsTradeValueMIN = Integer.parseInt(prop.getProperty("IsTradeValueMIN","0"));
		    		    	        ContinueTrade = Integer.parseInt(prop.getProperty("ContinueTrade","0"));

			    		    	    System.out.println("orderbook_S1:"+orderbook_S1 );
			    		    	    System.out.println("orderbook_E1:"+orderbook_E1 );
			    		    	    System.out.println("orderbook_S2:"+orderbook_S2 );
			    		    	    System.out.println("orderbook_E2:"+orderbook_E2 );
			    		    	    System.out.println("get_orderbook_coin_M:"+get_orderbook_coin_M );
			    		    	    System.out.println("trade_volume_max:"+trade_volume_max );
			    		    	    System.out.println("trade_volume_min:"+trade_volume_min );
			    		    	    System.out.println("start_time:"+start_time );
			    		    	    System.out.println("end_time:"+end_time );
			    		    	    System.out.println("Trace:"+Trace );
			    		    	    
			    		    	    System.out.println("BollUp:"+BollUp );
			    		    	    System.out.println("BollDown:"+BollDown );
			    		    	    System.out.println("volume:"+volume );
			    		    	    System.out.println("TradeOne:"+TradeOne );
			    		    	    System.out.println("IsTrade:"+IsTrade );
			    		    	    System.out.println("IsTradeValueMAX:"+IsTradeValueMAX );
			    		    	    System.out.println("IsTradeValueMIN:"+IsTradeValueMIN );
			    		    	    System.out.println("ContinueTrade:"+ContinueTrade );
			    		    	    
			    		    	    System.out.println("REFRESH_CNT:"+REFRESH_CNT );
			    		    	    
			    		    	    if( Double.parseDouble(candlestick.getHigh()) > BollUp || Double.parseDouble(candlestick.getLow()) < BollDown )
			    		    	    	BollCheck = 1;
			    		    	    else
			    		    	    	BollCheck = 0;
			    		    	    
		    		   		    } catch (BinanceApiException e) {
		    		   		        System.out.println("Failed to get candlestick data: " + e.getMessage());
		    		   		        volume = 0;
		    		   		        return;
		    		   		    }
		        			}
		        				
	        				trade_flag = 0;
		    				return;
		    			}
		
	    				if(checkhour.format(new Date(totalMilliseconds)).equals("05")) {
	    					Trade.INSTANCE.TradeStart(TRADE_STOP);
	    					System.exit(0);
	    				}
	    				
		    			init = false;
		    			AveCandleFlg = false;
		    			if(currentSecond < start_time || trade_flag != 0 || !(volume > trade_volume_min && volume < trade_volume_max) || BollCheck == 1) {
		    				if(Trace == 1)
		    					System.out.println("PASS currentSecond:"+currentSecond+" trade_flag:"+trade_flag+" volume:"+volume+" start_time:"+start_time+" BollCheck:"+BollCheck );
		    				return;
		    			}
		    			
		    		   	double trade_orderbook_down = 0;
		    		   	double trade_orderbook_up = 0;
		    		   			    		    
		    		    // Get the order book data
		    		    OrderBook orderBookF = clientF.getOrderBook(symbol, get_orderbook_coin_M);
		    		        
		    		    List<OrderBookEntry> bidsF = orderBookF.getBids();
		    		    List<OrderBookEntry> asksF = orderBookF.getAsks();
		    		        
		    		    for(int i=0; i < bidsF.size(); i++) {
		    		    	trade_orderbook_down += Double.parseDouble(bidsF.get(i).getQty());
		    		    }
		    		        
		    		    for(int i=0; i < asksF.size(); i++) {
		    		    	trade_orderbook_up += Double.parseDouble(asksF.get(i).getQty());
		    		    }
		    		        
		    		    if(!((orderbook_S1 < trade_orderbook_up && trade_orderbook_up < orderbook_E1) || (orderbook_S1 < trade_orderbook_down && trade_orderbook_down < orderbook_E1))) {
		    		    	if(Trace == 1)
			    		      	System.out.println("check false volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
		    		    	return;
		    		    }
		    		    	
		    		    trade_orderbook_up = roundToTwoDecimalPlaces(trade_orderbook_up);
		    		    trade_orderbook_down = roundToTwoDecimalPlaces(trade_orderbook_down);
		    		    
		    		    if(Trace == 1)
		    		      	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
		    		        		    		 	
		    		   	if( trade_flag == 0  &&
		        	   		(orderbook_S1 < trade_orderbook_up && trade_orderbook_up < orderbook_E1) && (orderbook_S2 < trade_orderbook_down && trade_orderbook_down < orderbook_E2)  ) {
			   		   		trade_flag = 1;
		               		if(IsTrade(currentSecond, 1)) {
				               	if(!btest ) 
				               		Trade.INSTANCE.TradeStart(BUY_UP);
			                		
				               	String str = timeFormat.format(new Date(totalMilliseconds));
				           	   	System.out.println("Trade Buy case S1: " + str);				
				               	System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
		               		}
		    		   	}else if( trade_flag == 0 && 
		    		   		(orderbook_S1 < trade_orderbook_down && trade_orderbook_down < orderbook_E1) && (orderbook_S2 < trade_orderbook_up && trade_orderbook_up < orderbook_E2) ) {
			    	   		trade_flag = 2;
		               		if(IsTrade(currentSecond, 2)) {
					           	if(!btest ) 
					           		Trade.INSTANCE.TradeStart(SELL_DOWN);
			                		
				               	String str = timeFormat.format(new Date(totalMilliseconds));
				           		System.out.println("Trade Sell case S1: " + str);			
				           		System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
		                	}
		    		   	}/*else if( trade_flag == 2 && TradeOne == 0 &&
			        		(orderbook_S1 < trade_orderbook_up && trade_orderbook_up < orderbook_E1) && (orderbook_S2 < trade_orderbook_down && trade_orderbook_down < orderbook_E2) ) {
			           		trade_flag = -3;
		           			if(IsTrade(currentSecond, 1)) {
					       		if(!btest )
		            				Trade.INSTANCE.TradeStart(BUY_UP);
			            			
				           		String str = timeFormat.format(new Date(totalMilliseconds));
				           		System.out.println("Trade Buy case S3: " + str);	    		    				
				       			System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
		            		}
		           		}else if( trade_flag == 1 && TradeOne == 0 &&
			    			(orderbook_S1 < trade_orderbook_down && trade_orderbook_down < orderbook_E1) && (orderbook_S2 < trade_orderbook_up && trade_orderbook_up < orderbook_E2) ) {
	        				trade_flag = -3;
		        			if(IsTrade(currentSecond, 2)) {
					      		if(!btest )
					      			Trade.INSTANCE.TradeStart(SELL_DOWN);
			           				
				           		String str = timeFormat.format(new Date(totalMilliseconds));
				          		System.out.println("Trade Sell case S3: " + str);	    		    				
				           		System.out.println("volume:"+volume+" trade_orderbook_up:"+trade_orderbook_up+" trade_orderbook_down:"+trade_orderbook_down);
			           		}
		           	    }*/
		            }
                }catch (Exception e) {
                    e.printStackTrace();
                    System.out.println(e.getMessage());
                }finally {
                    isRunning.set(false);
                }
            }
       };

       serviceGetData.scheduleAtFixedRate(runnableGetData, 0, 70, TimeUnit.MILLISECONDS);
        
		while (true) {
			try {
				Thread.sleep(300000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
    private static double roundToTwoDecimalPlaces(double value) {
        DecimalFormat df = new DecimalFormat("#.##");
        return Double.parseDouble(df.format(value));
    }
    
    private static boolean IsTrade(long currentSecond, int LS) {

    	System.out.println("==== IsTrade start ====");
    	
	    List<TradeHistoryItem> Trade = clientF.getTrades("BTCUSDT", 200);

	    int size = Trade.size();
    	long TradeTime = Trade.get(size-1).getTime();
    	long TradeSecond = (TradeTime/ 1000) % 60;
    	
	    double price = 0;
	    double pricecomp = 0;
	    if(size > 0)
	    	price = Double.parseDouble(Trade.get(size-1).getPrice());
	    
	    for(int i=size-1; i >= 0; i--) {
	    	long TradeTime_org = Trade.get(i).getTime();
	    	long TradeSecond_org = (TradeTime_org/ 1000) % 60;
	    	if(TradeSecond != TradeSecond_org) {
	    		pricecomp = Double.parseDouble(Trade.get(i).getPrice());
	    		break;
	    		
	    	}
	    }
	    
	    System.out.println("Price:"+price+" pricecomp:"+pricecomp+" currentSecond:"+currentSecond+" TradeSecond:"+TradeSecond);
	    
	    if(price == 0 || pricecomp == 0) {
		    if(ContinueTrade == 1)
		    	trade_flag = 0;
		    
	    	return false;
	    }
		
	    if(IsTrade == -1 && price != pricecomp && Math.abs(price - pricecomp) >= IsTradeValueMIN) {
	    	if((LS == 1 && price > pricecomp && (price - pricecomp) >= IsTradeValueMIN && (price - pricecomp) <= IsTradeValueMAX) ||
	    		(LS == 2 && price < pricecomp && (pricecomp - price) >= IsTradeValueMIN && (pricecomp - price) <= IsTradeValueMAX ) ) {	
			    double LastValue = 0;
			    double open = 0;
			    double close = 0;
				List<Candlestick> candlestickListF = clientF.getCandlestickBars("BTCUSDT", CandlestickInterval.ONE_MINUTE, 1, null, null);
				Candlestick candlestickF = null;
				for (int i = 0; i < candlestickListF.size(); i++) {
					candlestickF = candlestickListF.get(i);
					open = Double.valueOf(candlestickF.getOpen()).doubleValue();
					close = Double.valueOf(candlestickF.getClose()).doubleValue();
					LastValue = Math.abs(open - close);
				}
				
				AveValue = AveValue /1.2;
				System.out.println("open:"+open+" close:"+close+" AveValue:"+AveValue+" LastValue:"+LastValue+" LS:"+LS);
				
			    if(LS == 1 && ((AveValue > LastValue && close > open) || (AveValue < LastValue && close < open)) ) {
			    	return true;
			    }
			    
			    if(LS == 2 && ((AveValue > LastValue && open > close) || (AveValue < LastValue && open < close)) ) {
			    	return true;
			    }
	    	}else
	    		return false;
		    
	    }else {
		    if(IsTrade == 0 && price == pricecomp) {
		    	return true;
		    }
	    }
	    
	    if(ContinueTrade == 1)
	    	trade_flag = 0;
	    
	    return false;
    }
    
}